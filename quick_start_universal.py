#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 全站数据抓取工具快速启动脚本
"""

import asyncio
import logging
import sys
from pathlib import Path

# 导入主要模块
from universal_scraper import UniversalWebScraper
from data_exporter import DataExporter

def setup_quick_logging():
    """设置简化日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('quick_scraper.log', encoding='utf-8')
        ]
    )

async def quick_scrape_demo():
    """快速抓取演示 - 仅抓取少量数据用于测试"""
    print("🚀 启动快速抓取演示...")
    
    try:
        # 创建爬虫实例
        scraper = UniversalWebScraper()
        
        # 修改配置为演示模式
        scraper.crawl_config["max_depth"] = 2  # 限制抓取深度
        scraper.download_config["concurrent_downloads"] = 2  # 限制并发数
        
        print("📡 开始抓取数据...")
        
        # 执行抓取（限制页面数量）
        result_data = await scraper.scrape_universal_data(max_pages=5)
        
        print("💾 保存数据...")
        
        # 保存数据
        scraper.save_universal_data(result_data, "quick_demo_data.json")
        
        # 生成报告
        report = scraper.generate_universal_report(result_data)
        
        # 导出数据
        exporter = DataExporter()
        await exporter.export_json(result_data, report)
        await exporter.export_csv(result_data)
        
        # 显示结果
        print("\n" + "="*50)
        print("📊 快速抓取结果:")
        print("="*50)
        print(f"总页面数: {len(result_data.get('pages_data', []))}")
        print(f"下载资源数: {len(result_data.get('download_resources', []))}")
        print(f"发现链接数: {len(result_data.get('discovered_links', []))}")
        print(f"失败下载数: {len(result_data.get('failed_downloads', []))}")
        
        # 显示页面类型统计
        page_types = {}
        for page in result_data.get('pages_data', []):
            page_type = page.get('page_type', 'unknown')
            page_types[page_type] = page_types.get(page_type, 0) + 1
        
        print("\n页面类型分布:")
        for page_type, count in page_types.items():
            print(f"  {page_type}: {count}")
        
        # 显示下载类型统计
        download_types = {}
        for resource in result_data.get('download_resources', []):
            download_type = resource.get('download_type', 'unknown')
            download_types[download_type] = download_types.get(download_type, 0) + 1
        
        if download_types:
            print("\n下载类型分布:")
            for download_type, count in download_types.items():
                print(f"  {download_type}: {count}")
        
        print("\n📁 输出文件:")
        output_dir = Path("./output")
        if output_dir.exists():
            for file in output_dir.glob("*"):
                if file.is_file():
                    file_size = file.stat().st_size / 1024  # KB
                    print(f"  • {file.name} ({file_size:.1f}KB)")
        
        print("\n✅ 快速抓取演示完成！")
        return True
        
    except Exception as e:
        logging.error(f"快速抓取失败: {e}")
        print(f"❌ 抓取失败: {e}")
        return False

async def quick_test_components():
    """快速测试各组件功能"""
    print("🧪 开始组件功能测试...")
    
    try:
        # 测试1: 爬虫基础功能
        print("1️⃣ 测试爬虫基础功能...")
        scraper = UniversalWebScraper()
        
        # 测试链接过滤
        test_links = {
            "https://www.zhaozhongai.com/product/123.html",
            "https://www.zhaozhongai.com/download/",
            "https://www.zhaozhongai.com/contact/",
            "https://external.com/test.html"
        }
        
        filtered_links = scraper._filter_links(test_links, "https://www.zhaozhongai.com")
        print(f"   链接过滤测试: {len(filtered_links)}/{len(test_links)} 个链接通过")
        
        # 测试页面分类
        test_urls = [
            "https://www.zhaozhongai.com/product/123.html",
            "https://www.zhaozhongai.com/jiejinchuanganqi/",
            "https://www.zhaozhongai.com/download/"
        ]
        
        for url in test_urls:
            page_type = scraper.classify_page_type(url)
            print(f"   页面分类: {url} -> {page_type}")
        
        print("   ✅ 爬虫基础功能正常")
        
        # 测试2: 资源处理器
        print("\n2️⃣ 测试资源处理器...")
        from resource_processors import MetadataExtractor, PDFProcessor, ZIPProcessor
        
        metadata_extractor = MetadataExtractor()
        test_url = "https://www.zhaozhongai.com/test.pdf"
        metadata = metadata_extractor.extract_from_url(test_url)
        print(f"   元数据提取: {metadata['filename']} ({metadata['file_type']})")
        
        pdf_processor = PDFProcessor()
        print(f"   PDF处理器可用: {pdf_processor.available}")
        
        zip_processor = ZIPProcessor()
        print(f"   ZIP处理器支持格式: {zip_processor.supported_formats}")
        
        print("   ✅ 资源处理器正常")
        
        # 测试3: 数据导出器
        print("\n3️⃣ 测试数据导出器...")
        exporter = DataExporter()
        print(f"   Excel导出可用: {exporter.excel_available}")
        print(f"   输出目录: {exporter.output_dir}")
        
        print("   ✅ 数据导出器正常")
        
        print("\n🎉 所有组件测试通过！")
        return True
        
    except Exception as e:
        logging.error(f"组件测试失败: {e}")
        print(f"❌ 测试失败: {e}")
        return False

def print_quick_menu():
    """显示快速菜单"""
    menu = """
╔══════════════════════════════════════════════════════════════╗
║                    全站数据抓取工具                          ║
║                    快速启动菜单                              ║
╚══════════════════════════════════════════════════════════════╝

请选择操作:

1. 🚀 快速抓取演示 (推荐新用户)
   - 抓取少量数据进行功能演示
   - 快速了解工具能力

2. 🧪 组件功能测试
   - 测试各个模块是否正常工作
   - 检查依赖库安装情况

3. 📖 查看配置信息
   - 显示当前配置参数
   - 检查输出目录设置

4. 🔧 启动完整版本
   - 启动完整功能的主程序
   - 适合正式使用

0. 退出

请输入选项编号 (0-4): """
    
    return input(menu).strip()

def show_config_info():
    """显示配置信息"""
    from config import BASE_CONFIG, CRAWL_CONFIG, DOWNLOAD_CONFIG, OUTPUT_CONFIG
    
    print("\n📖 当前配置信息:")
    print("="*50)
    
    print("🌐 基础配置:")
    print(f"  目标网站: {BASE_CONFIG['base_url']}")
    print(f"  最大重试次数: {BASE_CONFIG['max_retries']}")
    print(f"  延迟范围: {BASE_CONFIG['delay_range']} 秒")
    
    print("\n🕷️ 爬取配置:")
    print(f"  最大深度: {CRAWL_CONFIG['max_depth']}")
    print(f"  产品类别数: {len(CRAWL_CONFIG['product_categories'])}")
    print(f"  每类别最大产品数: {CRAWL_CONFIG['max_products_per_category']}")
    
    print("\n📁 下载配置:")
    print(f"  下载目录: {DOWNLOAD_CONFIG['download_dir']}")
    print(f"  并发下载数: {DOWNLOAD_CONFIG['concurrent_downloads']}")
    print(f"  支持文件类型: {list(DOWNLOAD_CONFIG['supported_file_types'].keys())}")
    
    print("\n📊 输出配置:")
    print(f"  输出目录: {OUTPUT_CONFIG['output_dir']}")
    print(f"  输出格式: {OUTPUT_CONFIG['formats']}")
    
    print("="*50)

async def main():
    """主函数"""
    setup_quick_logging()
    
    while True:
        try:
            choice = print_quick_menu()
            
            if choice == '0':
                print("\n👋 感谢使用！")
                break
            elif choice == '1':
                await quick_scrape_demo()
            elif choice == '2':
                await quick_test_components()
            elif choice == '3':
                show_config_info()
            elif choice == '4':
                print("\n🔧 启动完整版本...")
                try:
                    from main_universal_scraper import main as main_program
                    await main_program()
                except ImportError:
                    print("❌ 无法导入主程序，请检查文件是否存在")
                except Exception as e:
                    print(f"❌ 启动主程序失败: {e}")
            else:
                print("❌ 无效选择，请重新输入")
            
            if choice != '0':
                input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            logging.error(f"程序异常: {e}")
            print(f"❌ 程序异常: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    asyncio.run(main())
