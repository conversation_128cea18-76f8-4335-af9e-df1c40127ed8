#!/bin/bash

echo "正在启动 MCP 系列服务..."

NPM_PATH="$APPDATA/npm"

"$NPM_PATH/context7-mcp.cmd" &> /dev/null &
echo "✔ 启动 context7-mcp"

"$NPM_PATH/server-memory.cmd" &> /dev/null &
echo "✔ 启动 server-memory"

"$NPM_PATH/server-sequential-thinking.cmd" &> /dev/null &
echo "✔ 启动 server-sequential-thinking"

"$NPM_PATH/playwright-mcp-server.cmd" &> /dev/null &
echo "✔ 启动 playwright-mcp-server"

"$NPM_PATH/mcp-shrimp-task-manager.cmd" &> /dev/null &
echo "✔ 启动 mcp-shrimp-task-manager"

echo "全部启动命令已发出，请在后台或任务管理器中查看。"

