@echo off
title Turbo Web Scraper v2.0

echo.
echo ========================================================
echo           Turbo Universal Web Scraper v2.0
echo ========================================================
echo.
echo Target: https://www.zhaozhongai.com
echo Developer: Su Yu
echo Features: Multi-browser concurrent scraping
echo Speed: 5-10x faster than original version
echo.

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    echo.
    echo Installation guide:
    echo 1. Visit https://www.python.org/downloads/
    echo 2. Download and install Python 3.7 or higher
    echo 3. Check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python environment OK
echo.

:menu
echo ========================================================
echo                    Launch Menu
echo ========================================================
echo.
echo 1. Turbo Concurrent Mode (Recommended) - 5-10x faster
echo 2. Quick Start Mode - For new users
echo 3. Full Feature Mode - Complete version
echo 4. Function Test - Check components
echo 5. Performance Test - Test different modes
echo 6. Speed Demo - Compare original vs concurrent
echo 7. Help - View instructions
echo 0. Exit
echo.
echo ========================================================
set /p choice="Please enter option number (0-7): "

if "%choice%"=="0" goto exit
if "%choice%"=="1" goto turbo
if "%choice%"=="2" goto quick
if "%choice%"=="3" goto full
if "%choice%"=="4" goto test
if "%choice%"=="5" goto performance
if "%choice%"=="6" goto demo
if "%choice%"=="7" goto help

echo Invalid choice. Please enter 0-7.
echo.
pause
goto menu

:turbo
echo.
echo Starting Turbo Concurrent Mode...
echo Tip: Multi-browser concurrent scraping, 5-10x speed boost
echo.
python turbo_scraper.py
goto end

:quick
echo.
echo Starting Quick Mode...
echo Tip: Suitable for new users with demo features
echo.
python quick_start_universal.py
goto end

:full
echo.
echo Starting Full Feature Mode...
echo Tip: Complete version with all features
echo.
python main_universal_scraper.py
goto end

:test
echo.
echo Running Function Test...
echo Tip: Check if all components work properly
echo.
python test_concurrent.py
goto end

:performance
echo.
echo Running Performance Test...
echo Tip: Test performance of different concurrent modes
echo.
python performance_test.py
goto end

:demo
echo.
echo Running Speed Demo...
echo Tip: Compare speed difference between original and concurrent
echo.
python 速度对比演示.py
goto end

:help
echo.
echo Help - Usage Instructions:
echo ========================================================
echo.
echo Mode Descriptions:
echo 1. Turbo Concurrent: Multi-browser concurrent scraping, 5-10x faster
echo 2. Quick Start: For new users with demo and basic testing
echo 3. Full Feature: Complete version with all scraping features
echo 4. Function Test: Check if all components work properly
echo 5. Performance Test: Test performance of different modes
echo 6. Speed Demo: Visual comparison of original vs concurrent
echo.
echo Output Files:
echo • ./output/universal_scraping_data.json - Complete data
echo • ./output/universal_scraping_data.csv - CSV format
echo • ./output/universal_scraping_analysis.xlsx - Excel analysis
echo • ./downloads/ - Downloaded files directory
echo.
echo Usage Tips:
echo • New users should first run "Function Test"
echo • Then try "Turbo Concurrent Mode" for best performance
echo • Use "Speed Demo" to see performance improvement
echo.
echo ========================================================
echo.
pause
goto menu

:end
echo.
echo ========================================================
echo                Program Execution Complete
echo ========================================================
echo.
echo Tips:
echo • Check ./output/ directory for output files
echo • Select appropriate mode to run again
echo • Use "Function Test" if you encounter problems
echo.
set /p continue="Return to main menu? (y/N): "
if /i "%continue%"=="y" goto menu
if /i "%continue%"=="yes" goto menu

:exit
echo.
echo Thank you for using Turbo Universal Web Scraper!
echo.
echo Project Features:
echo • 5-10x speed improvement with concurrent scraping
echo • Intelligent task distribution and load balancing
echo • Real-time performance monitoring and progress display
echo • Multiple modes for different requirements
echo.
echo Technical Support: Check user guide or contact developer
echo.
pause
exit /b 0
