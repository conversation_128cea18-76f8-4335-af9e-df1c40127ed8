# 全站数据抓取工具使用指南

## 📋 项目概述

本工具是基于现有传感器爬虫项目扩展开发的全站数据抓取工具，专门针对 https://www.zhaozhongai.com 网站进行全面的数据采集和分析。

### 🎯 主要功能

1. **全站页面抓取**
   - 自动发现所有产品页面和下载页面
   - 智能链接遍历和页面分类
   - 结构化数据提取

2. **智能资源处理**
   - PDF文件下载和内容提取
   - ZIP文件下载、解压和内容分析
   - 文件元数据提取和管理

3. **多格式数据输出**
   - JSON格式完整数据
   - CSV格式表格数据
   - Excel格式分析报告
   - 详细的统计分析

## 🚀 快速开始

### 环境要求

- Python 3.7+
- 已安装的依赖库（见下方安装说明）

### 安装依赖

```bash
pip install playwright pandas openpyxl PyPDF2 pdfplumber python-magic-bin aiofiles aiohttp rarfile
playwright install chromium
```

### 启动方式

#### 方式1: 批处理启动（推荐）
双击运行 `启动全站爬虫.bat` 文件

#### 方式2: 快速启动
```bash
python quick_start_universal.py
```

#### 方式3: 完整功能启动
```bash
python main_universal_scraper.py
```

#### 方式4: 运行测试
```bash
python test_universal_scraper.py
```

## 📁 项目结构

```
项目根目录/
├── universal_scraper.py          # 主爬虫类
├── resource_processors.py        # 资源处理器
├── data_exporter.py             # 数据导出器
├── config.py                    # 配置文件
├── main_universal_scraper.py     # 主启动脚本
├── quick_start_universal.py      # 快速启动脚本
├── test_universal_scraper.py     # 测试脚本
├── 启动全站爬虫.bat              # Windows批处理启动
├── 全站爬虫使用指南.md           # 本文档
└── 输出目录/
    ├── output/                   # 数据输出目录
    └── downloads/                # 文件下载目录
        ├── pdf/                  # PDF文件
        ├── zip/                  # ZIP文件
        └── extracted/            # 解压文件
```

## 🔧 功能详解

### 1. 全站链接发现

工具会自动从以下起始页面开始：
- `/product/` - 产品中心
- `/download/` - 资料下载

通过智能算法发现所有相关页面链接，包括：
- 产品类别页面
- 产品详情页面
- 下载资源页面

### 2. 页面类型识别

自动识别页面类型：
- `product_category` - 产品类别页面
- `product_detail` - 产品详情页面
- `download` - 下载页面
- `homepage` - 主页
- `other` - 其他页面

### 3. 数据提取

#### 产品信息提取
- 产品名称和型号
- 技术规格参数
- 产品描述
- 产品图片
- 下载链接

#### 下载资源处理
- **PDF文件**：下载并提取文本内容
- **ZIP文件**：下载、解压并分析内容结构
- **元数据**：文件大小、类型、哈希值等

### 4. 数据输出格式

#### JSON格式 (`universal_scraping_data.json`)
```json
{
  "metadata": {
    "scraped_at": "2025-08-01T10:00:00",
    "total_pages": 150,
    "total_downloads": 45
  },
  "pages_data": [...],
  "download_resources": [...],
  "failed_downloads": [...]
}
```

#### CSV格式 (`universal_scraping_data.csv`)
包含所有产品和下载资源的表格化数据

#### Excel格式 (`universal_scraping_analysis.xlsx`)
- **产品信息** - 详细产品数据
- **下载资源** - 资源文件信息
- **数据分析** - 统计图表
- **汇总报告** - 总体概况

## ⚙️ 配置说明

### 基础配置 (`config.py`)

```python
BASE_CONFIG = {
    "base_url": "https://www.zhaozhongai.com",
    "max_retries": 3,
    "delay_range": (1, 3)  # 随机延迟范围
}
```

### 爬取配置

```python
CRAWL_CONFIG = {
    "max_depth": 3,  # 最大爬取深度
    "max_products_per_category": 0  # 每类别最大产品数(0=无限制)
}
```

### 下载配置

```python
DOWNLOAD_CONFIG = {
    "concurrent_downloads": 5,  # 并发下载数
    "download_timeout": 300,    # 下载超时(秒)
    "file_size_limits": {       # 文件大小限制
        "pdf": 50 * 1024 * 1024,  # 50MB
        "zip": 200 * 1024 * 1024  # 200MB
    }
}
```

## 📊 使用模式

### 1. 完整全站抓取（推荐）
- 抓取所有页面数据
- 处理所有下载资源
- 生成完整分析报告
- 适合：正式数据采集

### 2. 仅抓取页面数据
- 只抓取页面结构化数据
- 不下载处理文件
- 快速获取概览信息
- 适合：快速调研

### 3. 仅处理下载资源
- 基于已有数据处理文件
- 补充处理之前的抓取结果
- 适合：资源处理补充

### 4. 自定义配置抓取
- 自定义抓取范围和参数
- 高级用户选项
- 适合：特定需求

## 🔍 结果分析

### 数据质量指标
- **页面覆盖率**：发现页面数/总页面数
- **数据完整度**：成功提取字段数/总字段数
- **下载成功率**：成功下载数/总下载数

### 统计信息
- 页面类型分布
- 产品类别统计
- 文件类型分析
- 处理时间统计

## ⚠️ 注意事项

### 1. 网络和性能
- 建议在网络稳定环境下运行
- 大量数据抓取可能需要较长时间
- 注意磁盘空间（下载文件可能较大）

### 2. 反爬虫应对
- 工具已内置随机延迟机制
- 使用真实浏览器用户代理
- 遵循网站robots.txt规则

### 3. 数据使用
- 抓取的数据仅供学习研究使用
- 请遵守网站使用条款
- 商业使用需获得授权

## 🐛 故障排除

### 常见问题

#### 1. 依赖库安装失败
```bash
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ [包名]
```

#### 2. Playwright浏览器下载失败
```bash
# 手动安装浏览器
playwright install chromium --with-deps
```

#### 3. PDF处理失败
- 检查PDF文件是否损坏
- 尝试不同的PDF处理库
- 查看错误日志获取详细信息

#### 4. 内存不足
- 减少并发下载数量
- 分批处理大量数据
- 增加系统虚拟内存

### 日志查看
- 主程序日志：`./output/universal_scraper.log`
- 快速启动日志：`quick_scraper.log`
- 错误信息会同时显示在控制台

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本检查组件状态
3. 检查网络连接和目标网站可访问性
4. 联系开发者获取技术支持

---

**开发者**: 苏昱  
**许可证**: http://www.178188.xyz  
**版本**: v1.0  
**更新日期**: 2025年8月1日
