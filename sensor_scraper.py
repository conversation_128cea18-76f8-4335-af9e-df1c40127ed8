#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年7月31日
模块说明: 兆众智能传感器产品数据爬取脚本 - 优化版
"""

import asyncio
import json
import re
import logging
import time
from datetime import datetime
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EnhancedSensorScraper:
    def __init__(self):
        self.base_url = "https://www.zhaozhongai.com"
        # 扩展所有传感器产品类别
        self.sensor_categories = {
            "接近传感器": "/jiejinchuanganqi/",
            "光电传感器": "/guangdianchuanganqi/",
            "激光光电传感器": "/jiguangguangdianchuanganqi/",
            "激光位移传感器": "/jiguangweiyichuanganqi/",
            "色标传感器": "/sebiaochuanganqi/",
            "光纤传感器": "/guangxianchuanganqi/",
            "微动/行程/限位开关": "/-chong--lu-huan--liao---/",
            "管道液位传感器": "/-chong--lu-bing--liao-donghuan-----/",
            "继电器": "/-chong--lu-juan----hong--hu-dian-/"
        }
        self.products_data = []
        self.failed_urls = []
        self.retry_count = 3
        self.delay_range = (1, 3)  # 随机延迟范围

    async def random_delay(self):
        """随机延迟，避免被限制"""
        delay = random.uniform(*self.delay_range)
        await asyncio.sleep(delay)

    async def retry_request(self, page, url, max_retries=3):
        """带重试机制的请求"""
        for attempt in range(max_retries):
            try:
                await page.goto(url, wait_until="networkidle", timeout=30000)
                return True
            except Exception as e:
                logging.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {url} - {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    self.failed_urls.append(url)
                    return False
        return False

    async def scrape_product_list(self, page, category_name, category_url):
        """爬取产品列表页面 - 增强版"""
        logging.info(f"开始爬取 {category_name} 产品列表...")

        full_url = urljoin(self.base_url, category_url)

        # 使用重试机制
        if not await self.retry_request(page, full_url):
            logging.error(f"无法访问 {category_name} 列表页面")
            return []

        await self.random_delay()

        # 尝试多种选择器策略
        selectors = [
            'li:has(a[href*="/product/"])',  # 包含产品链接的li元素
            '.product-item',
            'li[class*="product"]',
            'li:has(img)',  # 包含图片的li元素
            'li'  # 最后的备选方案
        ]

        products = []
        for selector in selectors:
            try:
                products = await page.query_selector_all(selector)
                if products:
                    logging.info(f"使用选择器 '{selector}' 找到 {len(products)} 个元素")
                    break
            except Exception as e:
                logging.debug(f"选择器 '{selector}' 失败: {e}")
                continue

        if not products:
            logging.warning(f"{category_name} 未找到产品元素")
            return []

        category_products = []

        for i, product in enumerate(products):
            try:
                # 提取产品链接
                link_element = await product.query_selector('a[href*="/product/"]')
                if not link_element:
                    continue

                product_url = await link_element.get_attribute('href')
                if not product_url:
                    continue

                # 提取产品名称 - 多种策略
                product_name = ""
                name_selectors = ['a', '.product-title', 'h3', 'h4', '[title]']
                for name_sel in name_selectors:
                    try:
                        name_element = await product.query_selector(name_sel)
                        if name_element:
                            name_text = await name_element.inner_text()
                            if name_text and name_text.strip():
                                product_name = name_text.strip()
                                break
                            # 尝试title属性
                            title_attr = await name_element.get_attribute('title')
                            if title_attr and title_attr.strip():
                                product_name = title_attr.strip()
                                break
                    except:
                        continue

                if not product_name:
                    continue

                # 提取产品描述
                descriptions = []
                desc_elements = await product.query_selector_all('p, .desc, .product-desc, small')
                for desc in desc_elements:
                    try:
                        desc_text = await desc.inner_text()
                        if desc_text and desc_text.strip() and len(desc_text.strip()) > 3:
                            descriptions.append(desc_text.strip())
                    except:
                        continue

                # 提取产品图片
                image_url = ""
                img_element = await product.query_selector('img')
                if img_element:
                    try:
                        image_url = await img_element.get_attribute('src')
                        if image_url and not image_url.startswith('http'):
                            image_url = urljoin(self.base_url, image_url)
                    except:
                        pass

                product_info = {
                    "category": category_name,
                    "name": product_name,
                    "url": urljoin(self.base_url, product_url),
                    "descriptions": descriptions,
                    "image": image_url,
                    "specifications": {},
                    "scraped_at": datetime.now().isoformat()
                }
                category_products.append(product_info)

                # 进度显示
                if (i + 1) % 10 == 0:
                    logging.info(f"{category_name} 已处理 {i + 1}/{len(products)} 个元素")

            except Exception as e:
                logging.error(f"处理产品时出错: {e}")
                continue

        logging.info(f"{category_name} 成功提取 {len(category_products)} 个产品")
        return category_products
    
    async def scrape_product_details(self, page, product):
        """爬取单个产品的详细信息 - 增强版"""
        try:
            logging.info(f"爬取产品详情: {product['name']}")

            # 使用重试机制访问产品详情页
            if not await self.retry_request(page, product['url']):
                logging.error(f"无法访问产品详情页: {product['name']}")
                return product

            await self.random_delay()

            # 提取技术规格 - 多种策略
            specs = {}

            # 策略1: 查找表格数据
            try:
                table_rows = await page.query_selector_all('table tr, .spec-table tr, .param-table tr')
                for row in table_rows:
                    cells = await row.query_selector_all('td, th')
                    if len(cells) >= 2:
                        try:
                            key = await cells[0].inner_text()
                            value = await cells[1].inner_text()
                            if key and value and key.strip() and value.strip():
                                specs[key.strip()] = value.strip()
                        except:
                            continue
            except Exception as e:
                logging.debug(f"表格解析失败: {e}")

            # 策略2: 查找规格列表和段落
            spec_selectors = [
                '.spec-item', '.param-item', '.specification',
                '.product-spec', '.tech-spec', 'li', 'p', 'div'
            ]

            for selector in spec_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        try:
                            text = await element.inner_text()
                            if not text or len(text.strip()) < 3:
                                continue

                            # 解析键值对格式
                            patterns = [
                                r'([^：:]+)[：:]\s*([^，,\n\r]+)',  # 中英文冒号
                                r'([^=]+)=\s*([^，,\n\r]+)',      # 等号
                                r'([^-]+)-\s*([^，,\n\r]+)'       # 短横线
                            ]

                            for pattern in patterns:
                                matches = re.findall(pattern, text)
                                for match in matches:
                                    key, value = match[0].strip(), match[1].strip()
                                    if key and value and len(key) < 50 and len(value) < 200:
                                        specs[key] = value
                        except:
                            continue
                except:
                    continue

            # 策略3: 特殊规格提取
            page_content = await page.content()

            # 检测距离
            distance_patterns = [
                r'检测距离[：:]\s*([^，,\n\r；;]+)',
                r'Detection\s+distance[：:]\s*([^，,\n\r；;]+)',
                r'(\d+(?:\.\d+)?(?:mm|cm|m))',
                r'(\d+~\d+(?:mm|cm|m))',
                r'(0~\d+(?:mm|cm|m))'
            ]

            for pattern in distance_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    specs['检测距离'] = matches[0].strip()
                    break

            # 输出方式
            output_patterns = [
                r'输出方式[：:]\s*([^，,\n\r；;]+)',
                r'Output[：:]\s*([^，,\n\r；;]+)',
                r'(NPN|PNP|继电器|Relay)',
            ]

            for pattern in output_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    specs['输出方式'] = matches[0].strip()
                    break

            # 工作电压
            voltage_patterns = [
                r'工作电压[：:]\s*([^，,\n\r；;]+)',
                r'电源电压[：:]\s*([^，,\n\r；;]+)',
                r'Supply\s+voltage[：:]\s*([^，,\n\r；;]+)',
                r'(\d+(?:\.\d+)?V(?:DC|AC)?)',
                r'(\d+~\d+V(?:DC|AC)?)'
            ]

            for pattern in voltage_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    specs['工作电压'] = matches[0].strip()
                    break

            product['specifications'] = specs

            # 提取价格信息（如果有）
            try:
                price_elements = await page.query_selector_all('.price, .cost, [class*="price"], [class*="cost"]')
                for price_elem in price_elements:
                    price_text = await price_elem.inner_text()
                    if price_text and ('￥' in price_text or '元' in price_text or '$' in price_text):
                        product['price'] = price_text.strip()
                        break
            except:
                pass

            # 提取技术文档链接
            try:
                doc_links = []
                link_elements = await page.query_selector_all('a[href*=".pdf"], a[href*="download"], a[href*="doc"]')
                for link in link_elements:
                    href = await link.get_attribute('href')
                    text = await link.inner_text()
                    if href and text:
                        doc_links.append({
                            'title': text.strip(),
                            'url': urljoin(self.base_url, href)
                        })
                if doc_links:
                    product['documents'] = doc_links
            except:
                pass

            # 提取更多描述信息
            desc_selectors = [
                '.product-desc', '.description', '.content p',
                '.product-info', '.detail', '.intro'
            ]

            additional_descriptions = []
            for selector in desc_selectors:
                try:
                    desc_elements = await page.query_selector_all(selector)
                    for desc in desc_elements:
                        desc_text = await desc.inner_text()
                        if (desc_text and desc_text.strip() and
                            len(desc_text.strip()) > 10 and
                            desc_text.strip() not in additional_descriptions):
                            additional_descriptions.append(desc_text.strip())
                except:
                    continue

            if additional_descriptions:
                product['descriptions'].extend(additional_descriptions)

            logging.info(f"成功爬取产品详情: {product['name']} (规格: {len(specs)})")
            return product

        except Exception as e:
            logging.error(f"爬取产品详情失败 {product['name']}: {e}")
            return product
    
    async def scrape_all_products(self, max_products_per_category=None):
        """爬取所有产品数据 - 增强版"""
        start_time = datetime.now()
        logging.info(f"开始爬取所有传感器产品数据 - {start_time}")

        async with async_playwright() as p:
            # 启动浏览器，配置更多选项
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled'
                ]
            )

            # 创建新页面并配置
            page = await browser.new_page()

            # 设置更真实的用户代理和其他头部
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })

            # 设置视口
            await page.set_viewport_size({"width": 1920, "height": 1080})

            all_products = []
            total_categories = len(self.sensor_categories)

            # 爬取每个类别的产品列表
            for idx, (category_name, category_url) in enumerate(self.sensor_categories.items(), 1):
                try:
                    logging.info(f"处理类别 {idx}/{total_categories}: {category_name}")

                    # 爬取产品列表
                    products = await self.scrape_product_list(page, category_name, category_url)

                    if not products:
                        logging.warning(f"{category_name} 未找到产品")
                        continue

                    all_products.extend(products)

                    # 决定爬取多少产品的详细信息
                    detail_count = min(len(products), max_products_per_category or 10)
                    logging.info(f"将爬取 {detail_count} 个产品的详细信息")

                    # 爬取产品详细信息
                    for i, product in enumerate(products[:detail_count]):
                        try:
                            logging.info(f"爬取详情 {i+1}/{detail_count}: {product['name']}")
                            await self.scrape_product_details(page, product)
                            await self.random_delay()  # 使用随机延迟

                            # 每爬取5个产品保存一次数据
                            if (i + 1) % 5 == 0:
                                self.save_data(f"temp_backup_{category_name}_{i+1}.json")

                        except Exception as e:
                            logging.error(f"爬取产品详情失败: {product['name']} - {e}")
                            continue

                    # 类别间延迟
                    if idx < total_categories:
                        logging.info(f"类别 {category_name} 完成，等待后继续...")
                        await asyncio.sleep(random.uniform(2, 5))

                except Exception as e:
                    logging.error(f"爬取类别 {category_name} 时出错: {e}")
                    continue

            await browser.close()

            # 统计信息
            end_time = datetime.now()
            duration = end_time - start_time

            logging.info(f"爬取完成！")
            logging.info(f"总耗时: {duration}")
            logging.info(f"成功爬取 {len(all_products)} 个产品")
            logging.info(f"失败URL数量: {len(self.failed_urls)}")

            if self.failed_urls:
                logging.warning(f"失败的URL: {self.failed_urls}")

            self.products_data = all_products
            return all_products
    
    def save_data(self, filename="sensor_products.json"):
        """保存数据到JSON文件 - 增强版"""
        try:
            # 添加元数据
            data_with_meta = {
                "metadata": {
                    "scraped_at": datetime.now().isoformat(),
                    "total_products": len(self.products_data),
                    "categories": list(self.sensor_categories.keys()),
                    "failed_urls_count": len(self.failed_urls),
                    "scraper_version": "2.0"
                },
                "failed_urls": self.failed_urls,
                "products": self.products_data
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data_with_meta, f, ensure_ascii=False, indent=2)
            logging.info(f"数据已保存到 {filename}")

        except Exception as e:
            logging.error(f"保存数据失败: {e}")

    def generate_summary(self):
        """生成详细数据摘要"""
        if not self.products_data:
            return {"error": "没有数据"}

        # 基础统计
        summary = {
            "总产品数": len(self.products_data),
            "按类别统计": {},
            "规格统计": {},
            "数据质量": {}
        }

        # 按类别统计
        for product in self.products_data:
            category = product.get('category', '未知')
            if category not in summary["按类别统计"]:
                summary["按类别统计"][category] = {
                    "产品数量": 0,
                    "有规格数据": 0,
                    "有图片": 0,
                    "有价格": 0
                }

            cat_stats = summary["按类别统计"][category]
            cat_stats["产品数量"] += 1

            if product.get('specifications'):
                cat_stats["有规格数据"] += 1
            if product.get('image'):
                cat_stats["有图片"] += 1
            if product.get('price'):
                cat_stats["有价格"] += 1

        # 规格字段统计
        spec_fields = {}
        for product in self.products_data:
            specs = product.get('specifications', {})
            for field in specs.keys():
                spec_fields[field] = spec_fields.get(field, 0) + 1

        summary["规格统计"] = dict(sorted(spec_fields.items(), key=lambda x: x[1], reverse=True)[:10])

        # 数据质量评估
        total = len(self.products_data)
        summary["数据质量"] = {
            "有规格数据比例": f"{sum(1 for p in self.products_data if p.get('specifications')) / total * 100:.1f}%",
            "有图片比例": f"{sum(1 for p in self.products_data if p.get('image')) / total * 100:.1f}%",
            "有描述比例": f"{sum(1 for p in self.products_data if p.get('descriptions')) / total * 100:.1f}%",
            "失败URL数量": len(self.failed_urls)
        }

        return summary

    def export_to_csv(self, filename="sensor_products.csv"):
        """导出为CSV格式"""
        try:
            import pandas as pd

            # 准备数据
            csv_data = []
            for product in self.products_data:
                row = {
                    '产品类别': product.get('category', ''),
                    '产品名称': product.get('name', ''),
                    '产品URL': product.get('url', ''),
                    '图片URL': product.get('image', ''),
                    '价格': product.get('price', ''),
                    '描述': '; '.join(product.get('descriptions', [])),
                }

                # 添加规格字段
                specs = product.get('specifications', {})
                for key, value in specs.items():
                    row[f'规格_{key}'] = value

                csv_data.append(row)

            df = pd.DataFrame(csv_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            logging.info(f"CSV数据已保存到 {filename}")

        except ImportError:
            logging.warning("pandas未安装，无法导出CSV格式")
        except Exception as e:
            logging.error(f"导出CSV失败: {e}")

async def main():
    """主函数 - 增强版"""
    scraper = EnhancedSensorScraper()

    logging.info("=== 兆众智能传感器数据爬取工具 v2.0 ===")
    logging.info(f"目标类别: {list(scraper.sensor_categories.keys())}")

    try:
        # 开始爬取，每个类别最多爬取10个产品的详细信息
        products = await scraper.scrape_all_products(max_products_per_category=10)

        if not products:
            logging.error("未获取到任何产品数据")
            return

        logging.info(f"爬取完成！共获取 {len(products)} 个产品")

        # 保存数据
        scraper.save_data("sensor_products_enhanced.json")

        # 导出CSV
        scraper.export_to_csv("sensor_products_enhanced.csv")

        # 显示摘要
        summary = scraper.generate_summary()
        logging.info("=== 数据摘要 ===")
        logging.info(json.dumps(summary, ensure_ascii=False, indent=2))

        # 保存摘要报告
        with open("scraping_report.json", 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logging.info("爬取任务完成！生成文件:")
        logging.info("- sensor_products_enhanced.json (完整数据)")
        logging.info("- sensor_products_enhanced.csv (CSV格式)")
        logging.info("- scraping_report.json (摘要报告)")
        logging.info("- scraper.log (日志文件)")

    except Exception as e:
        logging.error(f"爬取过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
