#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 数据导出器 - 多格式数据输出和分析报告生成
"""

import json
import csv
import logging
import os
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

try:
    import pandas as pd
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logging.warning("Excel处理库未安装，Excel导出功能将被禁用")

from config import OUTPUT_CONFIG

class DataExporter:
    """数据导出器"""
    
    def __init__(self):
        self.output_dir = Path(OUTPUT_CONFIG["output_dir"])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.excel_available = EXCEL_AVAILABLE
    
    async def export_all_formats(self, data: Dict, report: Dict):
        """导出所有格式的数据"""
        logging.info("开始导出多格式数据...")
        
        try:
            # 导出JSON格式
            await self.export_json(data, report)
            
            # 导出CSV格式
            await self.export_csv(data)
            
            # 导出Excel格式
            if self.excel_available:
                await self.export_excel(data, report)
            else:
                logging.warning("跳过Excel导出（库未安装）")
            
            # 导出分析报告
            await self.export_report(report)
            
            logging.info("多格式数据导出完成")
            
        except Exception as e:
            logging.error(f"数据导出失败: {e}")
            raise
    
    async def export_json(self, data: Dict, report: Dict):
        """导出JSON格式数据"""
        try:
            # 主数据文件
            main_file = self.output_dir / OUTPUT_CONFIG["filenames"]["json"]
            with open(main_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 报告文件
            report_file = self.output_dir / OUTPUT_CONFIG["filenames"]["report"]
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logging.info(f"JSON数据已导出: {main_file}, {report_file}")
            
        except Exception as e:
            logging.error(f"JSON导出失败: {e}")
            raise
    
    async def export_csv(self, data: Dict):
        """导出CSV格式数据"""
        try:
            csv_file = self.output_dir / OUTPUT_CONFIG["filenames"]["csv"]
            
            # 准备CSV数据
            csv_data = []
            
            # 处理页面数据
            for page in data.get("pages_data", []):
                if page.get("page_type") == "product_detail":
                    row = {
                        "类型": "产品详情",
                        "URL": page.get("url", ""),
                        "产品名称": page.get("name", ""),
                        "产品型号": page.get("model", ""),
                        "描述": "; ".join(page.get("descriptions", [])),
                        "规格数量": len(page.get("specifications", {})),
                        "下载链接数": len(page.get("download_links", [])),
                        "图片数量": len(page.get("images", [])),
                        "抓取时间": page.get("scraped_at", "")
                    }
                    
                    # 添加主要规格
                    specs = page.get("specifications", {})
                    for key in ["检测距离", "输出方式", "工作电压", "检测方式"]:
                        row[f"规格_{key}"] = specs.get(key, "")
                    
                    csv_data.append(row)
                
                elif page.get("page_type") == "product_category":
                    row = {
                        "类型": "产品类别",
                        "URL": page.get("url", ""),
                        "类别名称": page.get("category_name", ""),
                        "产品数量": page.get("products_count", 0),
                        "抓取时间": page.get("scraped_at", "")
                    }
                    csv_data.append(row)
            
            # 处理下载资源
            for resource in data.get("download_resources", []):
                row = {
                    "类型": "下载资源",
                    "URL": resource.get("url", ""),
                    "标题": resource.get("title", ""),
                    "文件名": resource.get("filename", ""),
                    "文件类型": resource.get("file_type", ""),
                    "下载类型": resource.get("download_type", ""),
                    "是否已处理": "是" if resource.get("processed") else "否",
                    "处理状态": "成功" if resource.get("processed") and not resource.get("processing_error") else "失败",
                    "发现时间": resource.get("found_at", "")
                }
                
                # 添加处理结果信息
                if resource.get("download_type") == "pdf":
                    row["页面数"] = resource.get("page_count", "")
                    row["有文本内容"] = "是" if resource.get("text_content") else "否"
                elif resource.get("download_type") == "zip":
                    row["文件数量"] = resource.get("total_files", "")
                    row["解压路径"] = resource.get("extracted_path", "")
                
                csv_data.append(row)
            
            # 写入CSV文件
            if csv_data:
                fieldnames = set()
                for row in csv_data:
                    fieldnames.update(row.keys())
                
                with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.DictWriter(f, fieldnames=list(fieldnames))
                    writer.writeheader()
                    writer.writerows(csv_data)
                
                logging.info(f"CSV数据已导出: {csv_file}")
            
        except Exception as e:
            logging.error(f"CSV导出失败: {e}")
            raise
    
    async def export_excel(self, data: Dict, report: Dict):
        """导出Excel格式数据"""
        if not self.excel_available:
            return
        
        try:
            excel_file = self.output_dir / OUTPUT_CONFIG["filenames"]["excel"]
            
            # 创建Excel工作簿
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                
                # 工作表1: 产品信息
                products_data = []
                for page in data.get("pages_data", []):
                    if page.get("page_type") == "product_detail":
                        product_row = {
                            "产品名称": page.get("name", ""),
                            "产品型号": page.get("model", ""),
                            "产品URL": page.get("url", ""),
                            "描述": "; ".join(page.get("descriptions", [])),
                            "规格数量": len(page.get("specifications", {})),
                            "下载链接数": len(page.get("download_links", [])),
                            "图片数量": len(page.get("images", [])),
                            "抓取时间": page.get("scraped_at", "")
                        }
                        
                        # 添加规格信息
                        specs = page.get("specifications", {})
                        for key, value in specs.items():
                            product_row[f"规格_{key}"] = value
                        
                        products_data.append(product_row)
                
                if products_data:
                    df_products = pd.DataFrame(products_data)
                    df_products.to_excel(writer, sheet_name=OUTPUT_CONFIG["excel_sheets"]["products"], index=False)
                
                # 工作表2: 下载资源
                downloads_data = []
                for resource in data.get("download_resources", []):
                    download_row = {
                        "标题": resource.get("title", ""),
                        "文件名": resource.get("filename", ""),
                        "文件类型": resource.get("file_type", ""),
                        "下载类型": resource.get("download_type", ""),
                        "URL": resource.get("url", ""),
                        "是否已处理": "是" if resource.get("processed") else "否",
                        "处理状态": "成功" if resource.get("processed") and not resource.get("processing_error") else "失败",
                        "错误信息": resource.get("processing_error", ""),
                        "发现时间": resource.get("found_at", "")
                    }
                    
                    # 添加文件元数据
                    metadata = resource.get("file_metadata", {})
                    if metadata:
                        download_row["文件大小(MB)"] = metadata.get("file_size_mb", "")
                        download_row["文件哈希"] = metadata.get("file_hash", "")
                    
                    # 添加处理结果
                    if resource.get("download_type") == "pdf":
                        download_row["页面数"] = resource.get("page_count", "")
                        download_row["处理方法"] = resource.get("processing_method", "")
                        download_row["有文本内容"] = "是" if resource.get("text_content") else "否"
                    elif resource.get("download_type") == "zip":
                        download_row["包含文件数"] = resource.get("total_files", "")
                        download_row["解压路径"] = resource.get("extracted_path", "")
                    
                    downloads_data.append(download_row)
                
                if downloads_data:
                    df_downloads = pd.DataFrame(downloads_data)
                    df_downloads.to_excel(writer, sheet_name=OUTPUT_CONFIG["excel_sheets"]["downloads"], index=False)
                
                # 工作表3: 数据分析
                analysis_data = []
                
                # 页面类型统计
                for page_type, count in report.get("page_types", {}).items():
                    analysis_data.append({
                        "分析类型": "页面类型",
                        "项目": page_type,
                        "数量": count,
                        "百分比": f"{(count / report['summary']['total_pages'] * 100):.1f}%" if report['summary']['total_pages'] > 0 else "0%"
                    })
                
                # 下载类型统计
                for download_type, count in report.get("download_types", {}).items():
                    analysis_data.append({
                        "分析类型": "下载类型",
                        "项目": download_type,
                        "数量": count,
                        "百分比": f"{(count / report['summary']['total_downloads'] * 100):.1f}%" if report['summary']['total_downloads'] > 0 else "0%"
                    })
                
                # 成功率统计
                for rate_type, rate_value in report.get("success_rates", {}).items():
                    analysis_data.append({
                        "分析类型": "成功率",
                        "项目": rate_type,
                        "数量": f"{rate_value}%",
                        "百分比": ""
                    })
                
                if analysis_data:
                    df_analysis = pd.DataFrame(analysis_data)
                    df_analysis.to_excel(writer, sheet_name=OUTPUT_CONFIG["excel_sheets"]["analysis"], index=False)
                
                # 工作表4: 汇总报告
                summary_data = [
                    {"指标": "总页面数量", "数值": report["summary"]["total_pages"]},
                    {"指标": "下载资源数", "数值": report["summary"]["total_downloads"]},
                    {"指标": "失败下载数", "数值": report["summary"]["failed_downloads"]},
                    {"指标": "发现链接数", "数值": report["summary"]["discovered_links"]},
                    {"指标": "抓取耗时", "数值": report["summary"]["scraping_duration"]},
                ]
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name=OUTPUT_CONFIG["excel_sheets"]["summary"], index=False)
            
            logging.info(f"Excel数据已导出: {excel_file}")
            
        except Exception as e:
            logging.error(f"Excel导出失败: {e}")
            raise
    
    async def export_report(self, report: Dict):
        """导出分析报告"""
        try:
            report_file = self.output_dir / "detailed_analysis_report.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("全站数据抓取分析报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 基本统计
                f.write("📊 基本统计\n")
                f.write("-" * 40 + "\n")
                summary = report.get("summary", {})
                for key, value in summary.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # 页面类型分析
                f.write("📄 页面类型分析\n")
                f.write("-" * 40 + "\n")
                for page_type, count in report.get("page_types", {}).items():
                    f.write(f"{page_type}: {count}\n")
                f.write("\n")
                
                # 下载类型分析
                f.write("📁 下载类型分析\n")
                f.write("-" * 40 + "\n")
                for download_type, count in report.get("download_types", {}).items():
                    f.write(f"{download_type}: {count}\n")
                f.write("\n")
                
                # 文件分析
                f.write("📋 文件分析\n")
                f.write("-" * 40 + "\n")
                file_analysis = report.get("file_analysis", {})
                for key, value in file_analysis.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # 成功率分析
                f.write("✅ 成功率分析\n")
                f.write("-" * 40 + "\n")
                for rate_type, rate_value in report.get("success_rates", {}).items():
                    f.write(f"{rate_type}: {rate_value}%\n")
                f.write("\n")
                
                f.write("=" * 80 + "\n")
                f.write("报告结束\n")
            
            logging.info(f"分析报告已导出: {report_file}")
            
        except Exception as e:
            logging.error(f"报告导出失败: {e}")
            raise
