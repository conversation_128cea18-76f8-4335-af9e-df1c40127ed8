#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 并发爬虫性能测试脚本
"""

import asyncio
import time
import logging
from datetime import datetime
from concurrent_scraper import ConcurrentUniversalScraper
from universal_scraper import UniversalWebScraper

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

async def test_concurrent_performance():
    """测试并发性能"""
    print("🧪 并发爬虫性能测试")
    print("=" * 60)
    
    # 测试不同并发模式
    modes = ["safe", "normal", "fast", "turbo"]
    results = {}
    
    for mode in modes:
        print(f"\n🔄 测试模式: {mode}")
        print("-" * 40)
        
        try:
            scraper = ConcurrentUniversalScraper(mode)
            
            start_time = time.time()
            result = await scraper.scrape_concurrent_data(max_pages=10)  # 限制页面数用于测试
            end_time = time.time()
            
            duration = end_time - start_time
            pages_count = len(result.get("pages_data", []))
            downloads_count = len(result.get("download_resources", []))
            
            results[mode] = {
                "duration": duration,
                "pages": pages_count,
                "downloads": downloads_count,
                "speed": pages_count / duration if duration > 0 else 0,
                "performance_stats": result.get("metadata", {}).get("performance_stats", {})
            }
            
            print(f"✅ 完成 - 耗时: {duration:.1f}s, 页面: {pages_count}, 速度: {pages_count/duration:.1f} 页面/秒")
            
        except Exception as e:
            print(f"❌ 失败: {e}")
            results[mode] = {"error": str(e)}
    
    # 显示对比结果
    print("\n📊 性能对比结果:")
    print("=" * 80)
    print(f"{'模式':<10} {'耗时(s)':<10} {'页面数':<10} {'下载数':<10} {'速度(页面/s)':<15} {'内存(MB)':<12}")
    print("-" * 80)
    
    for mode, result in results.items():
        if "error" not in result:
            perf = result.get("performance_stats", {})
            print(f"{mode:<10} {result['duration']:<10.1f} {result['pages']:<10} "
                  f"{result['downloads']:<10} {result['speed']:<15.2f} "
                  f"{perf.get('memory_mb', 0):<12.1f}")
        else:
            print(f"{mode:<10} {'ERROR':<10} {'-':<10} {'-':<10} {'-':<15} {'-':<12}")
    
    return results

async def test_vs_original():
    """对比原版爬虫性能"""
    print("\n🆚 并发版 vs 原版性能对比")
    print("=" * 60)
    
    test_pages = 5  # 测试页面数
    
    # 测试原版爬虫
    print("🔄 测试原版爬虫...")
    try:
        original_scraper = UniversalWebScraper()
        
        start_time = time.time()
        original_result = await original_scraper.scrape_universal_data(max_pages=test_pages)
        original_duration = time.time() - start_time
        
        original_pages = len(original_result.get("pages_data", []))
        print(f"✅ 原版完成 - 耗时: {original_duration:.1f}s, 页面: {original_pages}")
        
    except Exception as e:
        print(f"❌ 原版测试失败: {e}")
        original_duration = None
        original_pages = 0
    
    # 测试并发版爬虫
    print("🔄 测试并发版爬虫...")
    try:
        concurrent_scraper = ConcurrentUniversalScraper("fast")
        
        start_time = time.time()
        concurrent_result = await concurrent_scraper.scrape_concurrent_data(max_pages=test_pages)
        concurrent_duration = time.time() - start_time
        
        concurrent_pages = len(concurrent_result.get("pages_data", []))
        print(f"✅ 并发版完成 - 耗时: {concurrent_duration:.1f}s, 页面: {concurrent_pages}")
        
    except Exception as e:
        print(f"❌ 并发版测试失败: {e}")
        concurrent_duration = None
        concurrent_pages = 0
    
    # 对比结果
    if original_duration and concurrent_duration:
        speedup = original_duration / concurrent_duration
        print(f"\n📈 性能提升:")
        print(f"   原版耗时: {original_duration:.1f}s")
        print(f"   并发版耗时: {concurrent_duration:.1f}s")
        print(f"   性能提升: {speedup:.1f}x 倍")
        print(f"   时间节省: {((original_duration - concurrent_duration) / original_duration * 100):.1f}%")
    
    return {
        "original": {"duration": original_duration, "pages": original_pages},
        "concurrent": {"duration": concurrent_duration, "pages": concurrent_pages}
    }

async def test_resource_usage():
    """测试资源使用情况"""
    print("\n💾 资源使用测试")
    print("=" * 60)
    
    import psutil
    import gc
    
    # 获取初始资源状态
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    initial_cpu = process.cpu_percent()
    
    print(f"初始内存: {initial_memory:.1f} MB")
    print(f"初始CPU: {initial_cpu:.1f}%")
    
    # 运行并发爬虫
    print("\n🔄 运行并发爬虫...")
    
    try:
        scraper = ConcurrentUniversalScraper("fast")
        
        # 监控资源使用
        max_memory = initial_memory
        max_cpu = initial_cpu
        
        async def monitor_resources():
            nonlocal max_memory, max_cpu
            while True:
                try:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    current_cpu = process.cpu_percent()
                    
                    max_memory = max(max_memory, current_memory)
                    max_cpu = max(max_cpu, current_cpu)
                    
                    await asyncio.sleep(1)
                except asyncio.CancelledError:
                    break
        
        # 启动监控
        monitor_task = asyncio.create_task(monitor_resources())
        
        # 执行抓取
        result = await scraper.scrape_concurrent_data(max_pages=8)
        
        # 停止监控
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass
        
        # 强制垃圾回收
        gc.collect()
        
        # 获取最终资源状态
        final_memory = process.memory_info().rss / 1024 / 1024
        final_cpu = process.cpu_percent()
        
        print(f"\n📊 资源使用统计:")
        print(f"   最大内存: {max_memory:.1f} MB")
        print(f"   最大CPU: {max_cpu:.1f}%")
        print(f"   最终内存: {final_memory:.1f} MB")
        print(f"   内存增长: {final_memory - initial_memory:.1f} MB")
        
        # 检查是否有内存泄漏
        if final_memory > initial_memory * 1.5:
            print("⚠️  可能存在内存泄漏")
        else:
            print("✅ 内存使用正常")
        
        return {
            "initial_memory": initial_memory,
            "max_memory": max_memory,
            "final_memory": final_memory,
            "max_cpu": max_cpu
        }
        
    except Exception as e:
        print(f"❌ 资源测试失败: {e}")
        return None

def generate_performance_report(concurrent_results, comparison_results, resource_results):
    """生成性能测试报告"""
    report = {
        "test_time": datetime.now().isoformat(),
        "concurrent_performance": concurrent_results,
        "performance_comparison": comparison_results,
        "resource_usage": resource_results
    }
    
    # 保存报告
    import json
    with open("performance_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 性能测试报告已保存: performance_test_report.json")
    
    return report

async def main():
    """主测试函数"""
    setup_test_logging()
    
    print("🚀 并发爬虫性能测试套件")
    print("=" * 60)
    print("⚠️  注意: 此测试会实际访问目标网站，请确保网络连接正常")
    print("⏱️  预计测试时间: 5-10分钟")
    print()
    
    confirm = input("确认开始性能测试? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 测试取消")
        return
    
    start_time = time.time()
    
    try:
        # 1. 并发性能测试
        print("\n1️⃣ 并发性能测试")
        concurrent_results = await test_concurrent_performance()
        
        # 2. 对比测试
        print("\n2️⃣ 性能对比测试")
        comparison_results = await test_vs_original()
        
        # 3. 资源使用测试
        print("\n3️⃣ 资源使用测试")
        resource_results = await test_resource_usage()
        
        # 4. 生成报告
        print("\n4️⃣ 生成测试报告")
        report = generate_performance_report(concurrent_results, comparison_results, resource_results)
        
        # 总结
        total_time = time.time() - start_time
        print(f"\n🎉 性能测试完成!")
        print(f"⏱️  总测试时间: {total_time:.1f}s")
        
        # 推荐配置
        print(f"\n💡 推荐配置:")
        best_mode = None
        best_speed = 0
        
        for mode, result in concurrent_results.items():
            if "error" not in result and result["speed"] > best_speed:
                best_speed = result["speed"]
                best_mode = mode
        
        if best_mode:
            print(f"   最佳模式: {best_mode} (速度: {best_speed:.2f} 页面/秒)")
        
        if resource_results:
            if resource_results["max_memory"] < 500:
                print(f"   内存使用: 良好 ({resource_results['max_memory']:.1f}MB)")
            else:
                print(f"   内存使用: 较高 ({resource_results['max_memory']:.1f}MB)")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logging.error(f"性能测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
