#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 全站数据抓取工具主启动脚本
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# 导入自定义模块
from universal_scraper import UniversalWebScraper
from data_exporter import DataExporter
from config import OUTPUT_CONFIG, LOGGING_CONFIG

def setup_logging():
    """设置日志配置"""
    log_format = LOGGING_CONFIG["format"]
    log_level = getattr(logging, LOGGING_CONFIG["level"])
    
    # 创建日志目录
    log_dir = Path(OUTPUT_CONFIG["output_dir"])
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日志
    handlers = []
    
    if LOGGING_CONFIG["console_handler"]:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(log_format))
        handlers.append(console_handler)
    
    if LOGGING_CONFIG["file_handler"]:
        log_file = log_dir / OUTPUT_CONFIG["filenames"]["log"]
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        handlers.append(file_handler)
    
    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        format=log_format
    )

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    全站数据抓取工具 v1.0                      ║
║                Universal Web Scraper                         ║
║                                                              ║
║  目标网站: https://www.zhaozhongai.com                       ║
║  功能特性:                                                   ║
║    • 全站产品页面抓取                                        ║
║    • 下载资源智能识别                                        ║
║    • PDF文件内容提取                                         ║
║    • ZIP文件解压分析                                         ║
║    • 多格式数据输出                                          ║
║                                                              ║
║  开发者: 苏昱                                                ║
║  许可证: http://www.178188.xyz                               ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """打印操作菜单"""
    menu = """
请选择操作模式:

1. 🚀 完整全站抓取 (推荐)
   - 抓取所有产品页面和下载页面
   - 处理所有PDF和ZIP文件
   - 生成完整分析报告

2. 📄 仅抓取页面数据
   - 只抓取页面结构化数据
   - 不下载和处理文件
   - 快速获取概览信息

3. 📁 仅处理下载资源
   - 基于已有数据处理下载文件
   - 适用于补充处理场景

4. 🔧 自定义配置抓取
   - 自定义抓取范围和参数
   - 高级用户选项

5. 📊 查看历史报告
   - 查看之前的抓取结果
   - 数据分析和统计

0. 退出程序

请输入选项编号 (0-5): """
    
    return input(menu).strip()

async def mode_full_scraping():
    """完整全站抓取模式"""
    print("\n🚀 开始完整全站抓取...")
    
    scraper = UniversalWebScraper()
    exporter = DataExporter()
    
    try:
        # 执行全站抓取
        result_data = await scraper.scrape_universal_data()
        
        # 保存原始数据
        scraper.save_universal_data(result_data)
        
        # 生成分析报告
        report = scraper.generate_universal_report(result_data)
        
        # 导出多格式数据
        await exporter.export_all_formats(result_data, report)
        
        # 显示结果摘要
        print_result_summary(result_data, report)
        
        return True
        
    except Exception as e:
        logging.error(f"完整抓取失败: {e}")
        print(f"❌ 抓取失败: {e}")
        return False

async def mode_pages_only():
    """仅抓取页面数据模式"""
    print("\n📄 开始页面数据抓取...")
    
    scraper = UniversalWebScraper()
    
    try:
        # 临时禁用下载处理
        original_process = scraper._process_download_resources
        scraper._process_download_resources = lambda page: None
        
        # 执行页面抓取
        result_data = await scraper.scrape_universal_data()
        
        # 恢复原始方法
        scraper._process_download_resources = original_process
        
        # 保存数据
        scraper.save_universal_data(result_data, "pages_only_data.json")
        
        # 生成简化报告
        report = scraper.generate_universal_report(result_data)
        
        print_result_summary(result_data, report)
        
        return True
        
    except Exception as e:
        logging.error(f"页面抓取失败: {e}")
        print(f"❌ 抓取失败: {e}")
        return False

async def mode_downloads_only():
    """仅处理下载资源模式"""
    print("\n📁 开始处理下载资源...")
    
    # 检查是否有已存在的数据文件
    data_file = Path(OUTPUT_CONFIG["output_dir"]) / OUTPUT_CONFIG["filenames"]["json"]
    
    if not data_file.exists():
        print("❌ 未找到已有数据文件，请先执行页面抓取")
        return False
    
    try:
        # 加载已有数据
        with open(data_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        scraper = UniversalWebScraper()
        
        # 重新处理下载资源
        if result_data.get("download_resources"):
            scraper.download_resources = result_data["download_resources"]
            
            # 模拟页面对象（仅用于处理下载）
            class MockPage:
                pass
            
            await scraper._process_download_resources(MockPage())
            
            # 更新数据
            result_data["download_resources"] = scraper.download_resources
            result_data["failed_downloads"] = scraper.failed_downloads
            
            # 保存更新后的数据
            scraper.save_universal_data(result_data, "downloads_processed_data.json")
            
            print("✅ 下载资源处理完成")
            return True
        else:
            print("❌ 数据中未找到下载资源")
            return False
            
    except Exception as e:
        logging.error(f"下载处理失败: {e}")
        print(f"❌ 处理失败: {e}")
        return False

def mode_custom_config():
    """自定义配置抓取模式"""
    print("\n🔧 自定义配置抓取")
    print("此功能正在开发中，敬请期待...")
    return False

def mode_view_reports():
    """查看历史报告模式"""
    print("\n📊 查看历史报告")
    
    output_dir = Path(OUTPUT_CONFIG["output_dir"])
    
    # 查找报告文件
    report_files = list(output_dir.glob("*report*.json"))
    data_files = list(output_dir.glob("*data*.json"))
    
    if not report_files and not data_files:
        print("❌ 未找到历史报告文件")
        return False
    
    print("\n找到以下文件:")
    all_files = report_files + data_files
    for i, file in enumerate(all_files, 1):
        file_stat = file.stat()
        file_time = datetime.fromtimestamp(file_stat.st_mtime)
        file_size = file_stat.st_size / 1024  # KB
        print(f"{i}. {file.name} ({file_size:.1f}KB, {file_time.strftime('%Y-%m-%d %H:%M')})")
    
    try:
        choice = int(input(f"\n请选择要查看的文件 (1-{len(all_files)}): "))
        if 1 <= choice <= len(all_files):
            selected_file = all_files[choice - 1]
            
            with open(selected_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n📄 文件内容预览: {selected_file.name}")
            print("=" * 60)
            print(json.dumps(data, ensure_ascii=False, indent=2)[:2000])
            if len(json.dumps(data)) > 2000:
                print("\n... (内容过长，仅显示前2000字符)")
            
            return True
        else:
            print("❌ 无效选择")
            return False
            
    except (ValueError, KeyboardInterrupt):
        print("❌ 操作取消")
        return False

def print_result_summary(data: dict, report: dict):
    """打印结果摘要"""
    summary = report.get("summary", {})
    
    print("\n" + "="*60)
    print("📊 抓取结果摘要")
    print("="*60)
    print(f"总页面数量: {summary.get('total_pages', 0)}")
    print(f"下载资源数: {summary.get('total_downloads', 0)}")
    print(f"失败下载数: {summary.get('failed_downloads', 0)}")
    print(f"发现链接数: {summary.get('discovered_links', 0)}")
    print(f"抓取耗时: {summary.get('scraping_duration', 'unknown')}")
    
    if report.get("success_rates"):
        print(f"\n成功率统计:")
        for rate_type, rate_value in report["success_rates"].items():
            print(f"  {rate_type}: {rate_value}%")
    
    print("\n📁 输出文件:")
    output_dir = Path(OUTPUT_CONFIG["output_dir"])
    for file in output_dir.glob("*"):
        if file.is_file():
            file_size = file.stat().st_size / 1024  # KB
            print(f"  • {file.name} ({file_size:.1f}KB)")
    
    print("="*60)

async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 显示横幅
    print_banner()
    
    while True:
        try:
            choice = print_menu()
            
            if choice == '0':
                print("\n👋 感谢使用全站数据抓取工具！")
                break
            elif choice == '1':
                await mode_full_scraping()
            elif choice == '2':
                await mode_pages_only()
            elif choice == '3':
                await mode_downloads_only()
            elif choice == '4':
                mode_custom_config()
            elif choice == '5':
                mode_view_reports()
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            logging.error(f"程序异常: {e}")
            print(f"❌ 程序异常: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    asyncio.run(main())
