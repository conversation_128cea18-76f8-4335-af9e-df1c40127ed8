#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 爬虫速度对比演示 - 展示并发优化效果
"""

import asyncio
import time
import logging
from datetime import datetime

def setup_demo_logging():
    """设置演示日志"""
    logging.basicConfig(
        level=logging.WARNING,  # 减少日志输出
        format='%(levelname)s - %(message)s'
    )

def print_demo_banner():
    """打印演示横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 爬虫速度对比演示                       ║
║                  Speed Comparison Demo                      ║
║                                                              ║
║  对比原版爬虫 vs 并发爬虫的性能差异                          ║
║  展示多浏览器并发带来的速度提升                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def demo_original_scraper():
    """演示原版爬虫"""
    print("🐌 原版爬虫测试 (单浏览器串行)")
    print("-" * 50)
    
    try:
        from universal_scraper import UniversalWebScraper
        
        scraper = UniversalWebScraper()
        
        print("⏰ 开始时间:", datetime.now().strftime('%H:%M:%S'))
        start_time = time.time()
        
        # 限制页面数用于演示
        result = await scraper.scrape_universal_data(max_pages=3)
        
        end_time = time.time()
        duration = end_time - start_time
        
        pages_count = len(result.get("pages_data", []))
        downloads_count = len(result.get("download_resources", []))
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print(f"📄 抓取页面: {pages_count} 个")
        print(f"📁 下载资源: {downloads_count} 个")
        print(f"🚀 平均速度: {pages_count/duration:.2f} 页面/秒")
        
        return {
            "duration": duration,
            "pages": pages_count,
            "downloads": downloads_count,
            "speed": pages_count/duration if duration > 0 else 0
        }
        
    except Exception as e:
        print(f"❌ 原版爬虫测试失败: {e}")
        return None

async def demo_concurrent_scraper(mode="fast"):
    """演示并发爬虫"""
    print(f"\n⚡ 并发爬虫测试 (多浏览器并行 - {mode}模式)")
    print("-" * 50)
    
    try:
        from concurrent_scraper import ConcurrentUniversalScraper
        
        scraper = ConcurrentUniversalScraper(mode)
        
        print(f"📊 配置: {scraper.max_browsers} 个浏览器, {scraper.base_delay}s 延迟")
        print("⏰ 开始时间:", datetime.now().strftime('%H:%M:%S'))
        start_time = time.time()
        
        # 限制页面数用于演示
        result = await scraper.scrape_concurrent_data(max_pages=3)
        
        end_time = time.time()
        duration = end_time - start_time
        
        pages_count = len(result.get("pages_data", []))
        downloads_count = len(result.get("download_resources", []))
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print(f"📄 抓取页面: {pages_count} 个")
        print(f"📁 下载资源: {downloads_count} 个")
        print(f"🚀 平均速度: {pages_count/duration:.2f} 页面/秒")
        
        # 显示性能统计
        perf_stats = result.get("metadata", {}).get("performance_stats", {})
        if perf_stats:
            print(f"💾 内存使用: {perf_stats.get('memory_mb', 0):.1f} MB")
            print(f"🔥 CPU使用: {perf_stats.get('cpu_percent', 0):.1f}%")
        
        return {
            "duration": duration,
            "pages": pages_count,
            "downloads": downloads_count,
            "speed": pages_count/duration if duration > 0 else 0,
            "mode": mode,
            "browsers": scraper.max_browsers
        }
        
    except Exception as e:
        print(f"❌ 并发爬虫测试失败: {e}")
        return None

def show_comparison_results(original_result, concurrent_results):
    """显示对比结果"""
    print("\n" + "="*70)
    print("📊 性能对比结果")
    print("="*70)
    
    if not original_result:
        print("❌ 原版爬虫测试失败，无法进行对比")
        return
    
    print(f"{'模式':<12} {'耗时(s)':<10} {'页面数':<8} {'速度(页面/s)':<12} {'提升倍数':<10}")
    print("-"*70)
    
    # 原版结果
    print(f"{'原版':<12} {original_result['duration']:<10.1f} {original_result['pages']:<8} "
          f"{original_result['speed']:<12.2f} {'基准':<10}")
    
    # 并发结果
    for result in concurrent_results:
        if result:
            speedup = result['speed'] / original_result['speed'] if original_result['speed'] > 0 else 0
            time_saved = ((original_result['duration'] - result['duration']) / original_result['duration'] * 100) if original_result['duration'] > 0 else 0
            
            print(f"{result['mode']:<12} {result['duration']:<10.1f} {result['pages']:<8} "
                  f"{result['speed']:<12.2f} {speedup:<10.1f}x")
    
    print("-"*70)
    
    # 最佳结果
    best_result = max([r for r in concurrent_results if r], key=lambda x: x['speed'])
    if best_result:
        best_speedup = best_result['speed'] / original_result['speed']
        time_saved = ((original_result['duration'] - best_result['duration']) / original_result['duration'] * 100)
        
        print(f"\n🏆 最佳性能: {best_result['mode']} 模式")
        print(f"   速度提升: {best_speedup:.1f}x 倍")
        print(f"   时间节省: {time_saved:.1f}%")
        print(f"   并发浏览器: {best_result['browsers']} 个")

def show_usage_recommendations():
    """显示使用建议"""
    print("\n💡 使用建议:")
    print("="*50)
    print("🛡️  安全模式 (2浏览器): 网络不稳定或首次使用")
    print("🔧 标准模式 (3浏览器): 日常使用，稳定可靠")
    print("⚡ 快速模式 (5浏览器): 推荐模式，性能与稳定性平衡")
    print("🚀 极速模式 (10浏览器): 网络良好时的最快速度")
    print()
    print("📈 性能优化提示:")
    print("• 并发数不是越多越好，要根据网络和硬件情况调整")
    print("• 建议先用快速模式测试，再根据效果调整")
    print("• 大量数据抓取时注意磁盘空间和内存使用")
    print("• 遇到频繁失败时降低并发数或增加延迟")

async def main():
    """主演示函数"""
    setup_demo_logging()
    print_demo_banner()
    
    print("🎯 本演示将对比原版爬虫和并发爬虫的性能差异")
    print("⚠️  注意: 演示会实际访问目标网站，请确保网络连接正常")
    print("⏱️  预计演示时间: 3-5分钟")
    print()
    
    confirm = input("确认开始性能演示? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 演示取消")
        return
    
    print("\n🚀 开始性能对比演示...")
    print("="*70)
    
    try:
        # 1. 测试原版爬虫
        original_result = await demo_original_scraper()
        
        # 2. 测试不同并发模式
        concurrent_modes = ["safe", "fast", "turbo"]
        concurrent_results = []
        
        for mode in concurrent_modes:
            result = await demo_concurrent_scraper(mode)
            concurrent_results.append(result)
        
        # 3. 显示对比结果
        show_comparison_results(original_result, concurrent_results)
        
        # 4. 显示使用建议
        show_usage_recommendations()
        
        print("\n🎉 性能演示完成!")
        print("\n🚀 立即体验:")
        print("   python turbo_scraper.py  # 启动极速爬虫")
        print("   python quick_start_universal.py  # 快速体验")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        logging.error(f"演示失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
