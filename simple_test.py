#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单功能测试脚本
"""

def test_imports():
    """测试模块导入"""
    try:
        from universal_scraper import UniversalWebScraper
        print("✅ 主模块导入成功")
        
        from resource_processors import PDFProcessor, ZIPProcessor, MetadataExtractor
        print("✅ 资源处理器导入成功")
        
        from data_exporter import DataExporter
        print("✅ 数据导出器导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_basic_functions():
    """测试基础功能"""
    try:
        from universal_scraper import UniversalWebScraper
        
        # 创建爬虫实例
        scraper = UniversalWebScraper()
        print("✅ 爬虫实例创建成功")
        
        # 测试链接过滤
        test_links = {
            'https://www.zhaozhongai.com/product/123.html',
            'https://www.zhaozhongai.com/contact/',
            'https://external.com/test.html'
        }
        filtered = scraper._filter_links(test_links, 'https://www.zhaozhongai.com')
        print(f"✅ 链接过滤测试: {len(filtered)}/{len(test_links)} 个链接通过")
        
        # 测试页面分类
        page_type = scraper.classify_page_type('https://www.zhaozhongai.com/product/123.html')
        print(f"✅ 页面分类测试: product/123.html -> {page_type}")
        
        # 测试下载类型分类
        download_type = scraper._classify_download_type('test.pdf', 'PDF文档')
        print(f"✅ 下载类型测试: test.pdf -> {download_type}")
        
        return True
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False

def test_resource_processors():
    """测试资源处理器"""
    try:
        from resource_processors import MetadataExtractor
        
        # 测试元数据提取
        extractor = MetadataExtractor()
        metadata = extractor.extract_from_url('https://www.zhaozhongai.com/test.pdf')
        print(f"✅ 元数据提取测试: {metadata['filename']} ({metadata['file_type']})")
        
        # 测试文件类型检测
        file_type = extractor._detect_file_type('.pdf')
        print(f"✅ 文件类型检测: .pdf -> {file_type}")
        
        return True
    except Exception as e:
        print(f"❌ 资源处理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("全站数据抓取工具 - 简单功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("基础功能测试", test_basic_functions),
        ("资源处理器测试", test_resource_processors)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    main()
