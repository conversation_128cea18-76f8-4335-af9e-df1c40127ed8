#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 全站数据抓取工具配置文件
"""

# 基础配置
BASE_CONFIG = {
    "base_url": "https://www.zhaozhongai.com",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "timeout": 30000,
    "max_retries": 3,
    "delay_range": (1, 3),  # 随机延迟范围(秒)
}

# 全站抓取配置
CRAWL_CONFIG = {
    "target_sections": {
        "product": "/product/",
        "download": "/download/"
    },
    
    # 产品类别映射 (扩展现有类别)
    "product_categories": {
        "至璨 · CPU单元": "/productlist1/",
        "至璨 · 总线耦合器": "/productlist2/",
        "至璨 · IO模块": "/mokuai/",
        "至璨 · 接近传感器": "/jiejinchuanganqi/",
        "至璨 · 光电传感器": "/guangdianchuanganqi/",
        "至璨 · 继电器": "/-chong--lu-juan----hong--hu-dian-/",
        "至璨 · 激光光电传感器": "/jiguangguangdianchuanganqi/",
        "至璨 · 激光位移传感器": "/jiguangweiyichuanganqi/",
        "至璨 · 色标传感器": "/sebiaochuanganqi/",
        "至璨 · 光纤传感器": "/guangxianchuanganqi/",
        "至璨 · 微动/行程/限位开关": "/-chong--lu-huan--liao---/",
        "至璨 · 管道液位传感器": "/-chong--lu-bing--liao-donghuan-----/"
    },
    
    # 链接发现规则
    "link_patterns": [
        r'/product/\d+\.html',
        r'/productlist\d+/',
        r'/[a-zA-Z0-9\-_]+/',
        r'/download/',
        r'/.*\.html'
    ],
    
    # 排除的链接模式
    "exclude_patterns": [
        r'/contact',
        r'/about',
        r'/news',
        r'/guestbook',
        r'javascript:',
        r'mailto:',
        r'tel:',
        r'#'
    ],
    
    # 最大爬取深度
    "max_depth": 3,
    
    # 每个类别最大产品数量 (0表示无限制)
    "max_products_per_category": 0,
}

# 下载处理配置
DOWNLOAD_CONFIG = {
    # 下载目录
    "download_dir": "./downloads",
    
    # 支持的文件类型
    "supported_file_types": {
        "pdf": [".pdf"],
        "zip": [".zip", ".rar", ".7z"],
        "doc": [".doc", ".docx"],
        "image": [".jpg", ".jpeg", ".png", ".gif", ".bmp"],
        "other": [".txt", ".xml", ".json"]
    },
    
    # 下载按钮识别规则
    "download_button_selectors": [
        'a[href*=".pdf"]',
        'a[href*=".zip"]',
        'a[href*=".rar"]',
        'a[href*="download"]',
        '.download-btn',
        '.btn-download',
        '[class*="download"]',
        'a:contains("下载")',
        'a:contains("立即下载")',
        'a:contains("PDF")',
        'a:contains("资料")'
    ],
    
    # PDF处理配置
    "pdf_config": {
        "extract_text": True,
        "extract_images": False,
        "max_pages": 100,  # 最大处理页数
        "encoding": "utf-8"
    },
    
    # ZIP处理配置
    "zip_config": {
        "extract_files": True,
        "max_extract_size": 100 * 1024 * 1024,  # 100MB
        "analyze_content": True,
        "supported_archives": [".zip", ".rar", ".7z"]
    },
    
    # 文件大小限制
    "file_size_limits": {
        "pdf": 50 * 1024 * 1024,  # 50MB
        "zip": 200 * 1024 * 1024,  # 200MB
        "other": 10 * 1024 * 1024   # 10MB
    },
    
    # 并发下载配置
    "concurrent_downloads": 5,
    "download_timeout": 300,  # 5分钟
}

# 数据输出配置
OUTPUT_CONFIG = {
    "output_dir": "./output",
    "formats": ["json", "csv", "excel"],
    
    # 文件命名
    "filenames": {
        "json": "universal_scraping_data.json",
        "csv": "universal_scraping_data.csv",
        "excel": "universal_scraping_analysis.xlsx",
        "report": "scraping_report.json",
        "log": "universal_scraper.log"
    },
    
    # Excel工作表配置
    "excel_sheets": {
        "products": "产品信息",
        "downloads": "下载资源",
        "analysis": "数据分析",
        "summary": "汇总报告"
    }
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_handler": True,
    "console_handler": True,
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5
}

# 性能配置
PERFORMANCE_CONFIG = {
    "max_concurrent_pages": 10,
    "page_load_timeout": 30,
    "element_wait_timeout": 10,
    "memory_limit": 1024 * 1024 * 1024,  # 1GB
    "enable_cache": True,
    "cache_size": 100
}

# 高性能并发配置
CONCURRENT_CONFIG = {
    # 并发浏览器数量 (1-20)
    "max_browsers": 5,

    # 每个浏览器的页面数量
    "pages_per_browser": 2,

    # 动态延迟配置
    "dynamic_delay": {
        "enabled": True,
        "base_delay": 0.5,  # 基础延迟(秒)
        "max_delay": 2.0,   # 最大延迟(秒)
        "scale_factor": 0.1  # 延迟缩放因子
    },

    # 任务分配策略
    "task_distribution": {
        "strategy": "balanced",  # balanced, priority, random
        "batch_size": 10,        # 每批处理的任务数
        "max_retries": 3         # 失败任务最大重试次数
    },

    # 性能监控
    "monitoring": {
        "enabled": True,
        "progress_interval": 5,   # 进度更新间隔(秒)
        "stats_collection": True, # 收集性能统计
        "memory_check": True      # 内存使用检查
    },

    # 资源限制
    "resource_limits": {
        "max_memory_mb": 2048,    # 最大内存使用(MB)
        "max_cpu_percent": 80,    # 最大CPU使用率(%)
        "timeout_seconds": 1800   # 总超时时间(秒)
    },

    # 并发模式配置
    "modes": {
        "turbo": {
            "max_browsers": 10,
            "base_delay": 0.2,
            "description": "极速模式 - 最快速度"
        },
        "fast": {
            "max_browsers": 5,
            "base_delay": 0.5,
            "description": "快速模式 - 平衡速度和稳定性"
        },
        "normal": {
            "max_browsers": 3,
            "base_delay": 1.0,
            "description": "标准模式 - 稳定可靠"
        },
        "safe": {
            "max_browsers": 2,
            "base_delay": 2.0,
            "description": "安全模式 - 最大兼容性"
        }
    }
}
