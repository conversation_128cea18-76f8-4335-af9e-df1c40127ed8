#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 全站数据抓取工具 - 基于现有传感器爬虫扩展
"""

import asyncio
import json
import re
import logging
import time
import os
import mimetypes
from datetime import datetime
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse, unquote
from typing import Dict, List, Optional, Set, Tuple, Any
import random
from pathlib import Path
import aiofiles
import aiohttp
from collections import defaultdict

# 导入现有模块
from sensor_scraper import EnhancedSensorScraper
from config import (
    BASE_CONFIG, CRAWL_CONFIG, DOWNLOAD_CONFIG, 
    OUTPUT_CONFIG, LOGGING_CONFIG, PERFORMANCE_CONFIG
)

# 导入资源处理模块
from resource_processors import PDFProcessor, ZIPProcessor, MetadataExtractor

class UniversalWebScraper(EnhancedSensorScraper):
    """
    全站数据抓取工具
    继承现有传感器爬虫的所有功能，并扩展全站抓取能力
    """
    
    def __init__(self):
        super().__init__()
        
        # 扩展配置
        self.base_url = BASE_CONFIG["base_url"]
        self.crawl_config = CRAWL_CONFIG
        self.download_config = DOWNLOAD_CONFIG
        self.output_config = OUTPUT_CONFIG
        
        # 全站数据存储
        self.all_pages_data = []
        self.download_resources = []
        self.discovered_links = set()
        self.processed_links = set()
        self.failed_downloads = []
        
        # 资源处理器
        self.pdf_processor = PDFProcessor()
        self.zip_processor = ZIPProcessor()
        self.metadata_extractor = MetadataExtractor()
        
        # 创建输出目录
        self._create_directories()
        
        logging.info("UniversalWebScraper 初始化完成")
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.download_config["download_dir"],
            self.output_config["output_dir"],
            os.path.join(self.download_config["download_dir"], "pdf"),
            os.path.join(self.download_config["download_dir"], "zip"),
            os.path.join(self.download_config["download_dir"], "extracted")
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    async def discover_all_links(self, page, start_urls: List[str]) -> Set[str]:
        """
        智能链接发现器
        从起始URL开始，发现所有相关页面链接
        """
        logging.info("开始全站链接发现...")
        
        discovered = set()
        to_process = set(start_urls)
        processed = set()
        depth = 0
        max_depth = self.crawl_config["max_depth"]
        
        while to_process and depth < max_depth:
            current_level = list(to_process)
            to_process.clear()
            
            logging.info(f"处理第 {depth + 1} 层链接，共 {len(current_level)} 个")
            
            for url in current_level:
                if url in processed:
                    continue
                    
                try:
                    # 访问页面
                    if not await self.retry_request(page, url):
                        continue
                    
                    await self.random_delay()
                    
                    # 提取页面中的所有链接
                    links = await self._extract_page_links(page, url)
                    
                    # 过滤和验证链接
                    valid_links = self._filter_links(links, url)
                    
                    discovered.update(valid_links)
                    
                    # 添加新发现的链接到下一层处理
                    new_links = valid_links - processed
                    to_process.update(new_links)
                    
                    processed.add(url)
                    
                    logging.info(f"从 {url} 发现 {len(valid_links)} 个有效链接")
                    
                except Exception as e:
                    logging.error(f"处理链接 {url} 时出错: {e}")
                    continue
            
            depth += 1
        
        logging.info(f"链接发现完成，共发现 {len(discovered)} 个链接")
        self.discovered_links = discovered
        return discovered
    
    async def _extract_page_links(self, page, current_url: str) -> Set[str]:
        """从页面中提取所有链接"""
        links = set()
        
        try:
            # 获取所有a标签的href属性
            link_elements = await page.query_selector_all('a[href]')
            
            for element in link_elements:
                href = await element.get_attribute('href')
                if href:
                    # 转换为绝对URL
                    absolute_url = urljoin(current_url, href)
                    links.add(absolute_url)
            
            # 特殊处理：查找JavaScript中的链接
            js_links = await self._extract_js_links(page)
            links.update(js_links)
            
        except Exception as e:
            logging.error(f"提取页面链接失败: {e}")
        
        return links
    
    async def _extract_js_links(self, page) -> Set[str]:
        """从JavaScript中提取链接"""
        js_links = set()
        
        try:
            # 执行JavaScript获取动态链接
            js_result = await page.evaluate("""
                () => {
                    const links = [];
                    // 查找onclick事件中的链接
                    document.querySelectorAll('[onclick]').forEach(el => {
                        const onclick = el.getAttribute('onclick');
                        const match = onclick.match(/location\.href\s*=\s*['"]([^'"]+)['"]/);
                        if (match) links.push(match[1]);
                    });
                    return links;
                }
            """)
            
            for link in js_result:
                absolute_url = urljoin(self.base_url, link)
                js_links.add(absolute_url)
                
        except Exception as e:
            logging.debug(f"提取JavaScript链接失败: {e}")
        
        return js_links
    
    def _filter_links(self, links: Set[str], base_url: str) -> Set[str]:
        """过滤和验证链接"""
        valid_links = set()
        
        for link in links:
            # 基本验证
            if not link or not link.startswith(('http://', 'https://')):
                continue
            
            # 必须是同域名
            if not link.startswith(self.base_url):
                continue
            
            # 检查排除模式
            if self._should_exclude_link(link):
                continue
            
            # 检查包含模式
            if self._should_include_link(link):
                valid_links.add(link)
        
        return valid_links
    
    def _should_exclude_link(self, link: str) -> bool:
        """检查链接是否应该被排除"""
        exclude_patterns = self.crawl_config["exclude_patterns"]
        
        for pattern in exclude_patterns:
            if re.search(pattern, link, re.IGNORECASE):
                return True
        
        return False
    
    def _should_include_link(self, link: str) -> bool:
        """检查链接是否应该被包含"""
        include_patterns = self.crawl_config["link_patterns"]
        
        # 如果没有包含模式，默认包含
        if not include_patterns:
            return True
        
        for pattern in include_patterns:
            if re.search(pattern, link, re.IGNORECASE):
                return True
        
        return False
    
    def classify_page_type(self, url: str, page_content: str = None) -> str:
        """
        页面类型分类器
        根据URL和页面内容判断页面类型
        """
        url_lower = url.lower()
        
        # 产品页面
        if any(category_url in url for category_url in self.crawl_config["product_categories"].values()):
            return "product_category"
        
        if re.search(r'/product/\d+\.html', url):
            return "product_detail"
        
        # 下载页面
        if '/download' in url_lower:
            return "download"
        
        # 主页面
        if url == self.base_url or url == self.base_url + '/':
            return "homepage"
        
        # 其他页面
        return "other"
    
    async def scrape_universal_data(self, max_pages: int = 0) -> Dict:
        """
        全站数据抓取主函数
        """
        start_time = datetime.now()
        logging.info(f"开始全站数据抓取 - {start_time}")
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled'
                ]
            )
            
            page = await browser.new_page()
            
            # 设置用户代理和其他头部
            await page.set_extra_http_headers({
                'User-Agent': BASE_CONFIG["user_agent"],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })
            
            await page.set_viewport_size({"width": 1920, "height": 1080})
            
            try:
                # 1. 发现所有链接
                start_urls = [
                    self.base_url + self.crawl_config["target_sections"]["product"],
                    self.base_url + self.crawl_config["target_sections"]["download"]
                ]
                
                discovered_links = await self.discover_all_links(page, start_urls)
                
                # 2. 按类型分类链接
                categorized_links = self._categorize_links(discovered_links)
                
                # 3. 抓取各类型页面数据
                await self._scrape_categorized_pages(page, categorized_links, max_pages)
                
                # 4. 处理下载资源
                await self._process_download_resources(page)
                
            except Exception as e:
                logging.error(f"全站抓取过程中发生错误: {e}")
                raise
            finally:
                await browser.close()
        
        # 5. 生成最终报告
        end_time = datetime.now()
        duration = end_time - start_time
        
        result = {
            "metadata": {
                "scraped_at": end_time.isoformat(),
                "duration": str(duration),
                "total_pages": len(self.all_pages_data),
                "total_downloads": len(self.download_resources),
                "failed_downloads": len(self.failed_downloads),
                "scraper_version": "Universal 1.0"
            },
            "pages_data": self.all_pages_data,
            "download_resources": self.download_resources,
            "failed_downloads": self.failed_downloads,
            "discovered_links": list(self.discovered_links)
        }
        
        logging.info(f"全站抓取完成！耗时: {duration}")
        logging.info(f"成功抓取 {len(self.all_pages_data)} 个页面")
        logging.info(f"处理 {len(self.download_resources)} 个下载资源")
        
        return result

    def _categorize_links(self, links: Set[str]) -> Dict[str, List[str]]:
        """将链接按类型分类"""
        categorized = {
            "product_category": [],
            "product_detail": [],
            "download": [],
            "homepage": [],
            "other": []
        }

        for link in links:
            page_type = self.classify_page_type(link)
            categorized[page_type].append(link)

        logging.info("链接分类完成:")
        for category, urls in categorized.items():
            logging.info(f"  {category}: {len(urls)} 个链接")

        return categorized

    async def _scrape_categorized_pages(self, page, categorized_links: Dict, max_pages: int):
        """按分类抓取页面数据"""

        # 1. 抓取产品类别页面
        for url in categorized_links["product_category"]:
            try:
                await self._scrape_product_category_page(page, url)
                await self.random_delay()
            except Exception as e:
                logging.error(f"抓取产品类别页面失败 {url}: {e}")

        # 2. 抓取产品详情页面
        detail_urls = categorized_links["product_detail"]
        if max_pages > 0:
            detail_urls = detail_urls[:max_pages]

        for url in detail_urls:
            try:
                await self._scrape_product_detail_page(page, url)
                await self.random_delay()
            except Exception as e:
                logging.error(f"抓取产品详情页面失败 {url}: {e}")

        # 3. 抓取下载页面
        for url in categorized_links["download"]:
            try:
                await self._scrape_download_page(page, url)
                await self.random_delay()
            except Exception as e:
                logging.error(f"抓取下载页面失败 {url}: {e}")

    async def _scrape_product_category_page(self, page, url: str):
        """抓取产品类别页面"""
        logging.info(f"抓取产品类别页面: {url}")

        if not await self.retry_request(page, url):
            return

        try:
            # 提取类别信息
            category_name = await self._extract_category_name(page, url)

            # 提取产品列表
            products = await self._extract_product_list_from_page(page, url)

            page_data = {
                "url": url,
                "page_type": "product_category",
                "category_name": category_name,
                "products_count": len(products),
                "products": products,
                "scraped_at": datetime.now().isoformat()
            }

            self.all_pages_data.append(page_data)
            logging.info(f"产品类别页面抓取完成: {category_name}, 产品数: {len(products)}")

        except Exception as e:
            logging.error(f"抓取产品类别页面数据失败: {e}")

    async def _scrape_product_detail_page(self, page, url: str):
        """抓取产品详情页面"""
        logging.info(f"抓取产品详情页面: {url}")

        if not await self.retry_request(page, url):
            return

        try:
            # 使用现有的产品详情抓取逻辑
            product_data = {
                "url": url,
                "page_type": "product_detail",
                "scraped_at": datetime.now().isoformat()
            }

            # 提取产品基本信息
            product_data.update(await self._extract_product_basic_info(page))

            # 提取技术规格
            product_data["specifications"] = await self._extract_product_specifications(page)

            # 提取下载链接
            product_data["download_links"] = await self._extract_download_links(page)

            # 提取图片
            product_data["images"] = await self._extract_product_images(page)

            self.all_pages_data.append(product_data)
            logging.info(f"产品详情页面抓取完成: {product_data.get('name', 'Unknown')}")

        except Exception as e:
            logging.error(f"抓取产品详情页面数据失败: {e}")

    async def _scrape_download_page(self, page, url: str):
        """抓取下载页面"""
        logging.info(f"抓取下载页面: {url}")

        if not await self.retry_request(page, url):
            return

        try:
            # 提取下载资源信息
            download_items = await self._extract_download_items(page)

            page_data = {
                "url": url,
                "page_type": "download",
                "download_items_count": len(download_items),
                "download_items": download_items,
                "scraped_at": datetime.now().isoformat()
            }

            self.all_pages_data.append(page_data)

            # 将下载项目添加到资源列表
            self.download_resources.extend(download_items)

            logging.info(f"下载页面抓取完成，发现 {len(download_items)} 个下载项目")

        except Exception as e:
            logging.error(f"抓取下载页面数据失败: {e}")

    async def _extract_category_name(self, page, url: str) -> str:
        """提取类别名称"""
        try:
            # 尝试多种选择器
            selectors = [
                'h1', '.page-title', '.category-title',
                '.breadcrumb li:last-child', 'title'
            ]

            for selector in selectors:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        return text.strip()

            # 从URL推断类别名称
            for category, category_url in self.crawl_config["product_categories"].items():
                if category_url in url:
                    return category

            return "未知类别"

        except Exception as e:
            logging.error(f"提取类别名称失败: {e}")
            return "未知类别"

    async def _extract_product_list_from_page(self, page, url: str) -> List[Dict]:
        """从页面提取产品列表"""
        products = []

        try:
            # 使用现有的产品列表抓取逻辑，但简化版本
            selectors = [
                'li:has(a[href*="/product/"])',
                '.product-item',
                'li[class*="product"]',
                'li:has(img)'
            ]

            product_elements = []
            for selector in selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        product_elements = elements
                        break
                except:
                    continue

            for element in product_elements:
                try:
                    # 提取产品链接
                    link_element = await element.query_selector('a[href*="/product/"]')
                    if not link_element:
                        continue

                    product_url = await link_element.get_attribute('href')
                    if not product_url:
                        continue

                    # 提取产品名称
                    name_element = await element.query_selector('a, .product-title, h3, h4')
                    product_name = ""
                    if name_element:
                        product_name = await name_element.inner_text()

                    # 提取产品图片
                    img_element = await element.query_selector('img')
                    image_url = ""
                    if img_element:
                        image_url = await img_element.get_attribute('src')
                        if image_url and not image_url.startswith('http'):
                            image_url = urljoin(self.base_url, image_url)

                    product_info = {
                        "name": product_name.strip() if product_name else "",
                        "url": urljoin(self.base_url, product_url),
                        "image": image_url,
                        "found_on_page": url
                    }

                    products.append(product_info)

                except Exception as e:
                    logging.debug(f"处理产品元素时出错: {e}")
                    continue

        except Exception as e:
            logging.error(f"提取产品列表失败: {e}")

        return products

    async def _extract_product_basic_info(self, page) -> Dict[str, Any]:
        """提取产品基本信息"""
        info = {}

        try:
            # 产品名称
            name_selectors = ['h1', '.product-title', '.product-name', 'title']
            for selector in name_selectors:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        info["name"] = text.strip()
                        break

            # 产品描述
            desc_selectors = ['.product-desc', '.description', '.product-info p']
            descriptions = []
            for selector in desc_selectors:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    if text and text.strip() and len(text.strip()) > 10:
                        descriptions.append(text.strip())

            info["descriptions"] = descriptions

            # 产品型号
            model_patterns = [
                r'型号[：:]\s*([A-Z0-9\-]+)',
                r'Model[：:]\s*([A-Z0-9\-]+)',
                r'([A-Z]+\d+[A-Z0-9\-]*)'
            ]

            page_content = await page.content()
            for pattern in model_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    info["model"] = matches[0]
                    break

        except Exception as e:
            logging.error(f"提取产品基本信息失败: {e}")

        return info

    async def _extract_product_specifications(self, page) -> Dict[str, str]:
        """提取产品技术规格"""
        specs = {}

        try:
            # 使用现有的规格提取逻辑
            # 策略1: 查找表格数据
            table_rows = await page.query_selector_all('table tr, .spec-table tr, .param-table tr')
            for row in table_rows:
                cells = await row.query_selector_all('td, th')
                if len(cells) >= 2:
                    try:
                        key = await cells[0].inner_text()
                        value = await cells[1].inner_text()
                        if key and value and key.strip() and value.strip():
                            specs[key.strip()] = value.strip()
                    except:
                        continue

            # 策略2: 查找规格列表
            spec_elements = await page.query_selector_all('.spec-item, .param-item, li, p')
            for element in spec_elements:
                try:
                    text = await element.inner_text()
                    if not text or len(text.strip()) < 3:
                        continue

                    # 解析键值对格式
                    patterns = [
                        r'([^：:]+)[：:]\s*([^，,\n\r]+)',
                        r'([^=]+)=\s*([^，,\n\r]+)',
                        r'([^-]+)-\s*([^，,\n\r]+)'
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, text)
                        for match in matches:
                            key, value = match[0].strip(), match[1].strip()
                            if key and value and len(key) < 50 and len(value) < 200:
                                specs[key] = value
                except:
                    continue

        except Exception as e:
            logging.error(f"提取产品规格失败: {e}")

        return specs

    async def _extract_download_links(self, page) -> List[Dict[str, str]]:
        """提取下载链接"""
        download_links = []

        try:
            # 使用配置中的下载按钮选择器
            selectors = self.download_config["download_button_selectors"]

            for selector in selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        href = await element.get_attribute('href')
                        text = await element.inner_text()

                        if href and text:
                            download_info = {
                                "url": urljoin(self.base_url, href),
                                "text": text.strip(),
                                "type": self._classify_download_type(href, text)
                            }
                            download_links.append(download_info)
                except:
                    continue

        except Exception as e:
            logging.error(f"提取下载链接失败: {e}")

        return download_links

    def _classify_download_type(self, href: str, text: str) -> str:
        """分类下载类型"""
        href_lower = href.lower()
        text_lower = text.lower()

        if '.pdf' in href_lower or 'pdf' in text_lower:
            return "pdf"
        elif any(ext in href_lower for ext in ['.zip', '.rar', '.7z']):
            return "zip"
        elif 'download' in href_lower or '下载' in text_lower:
            return "download"
        else:
            return "other"

    async def _extract_product_images(self, page) -> List[str]:
        """提取产品图片"""
        images = []

        try:
            img_elements = await page.query_selector_all('img')
            for img in img_elements:
                src = await img.get_attribute('src')
                if src:
                    # 过滤掉小图标和装饰图片
                    if any(keyword in src.lower() for keyword in ['icon', 'logo', 'banner']):
                        continue

                    absolute_url = urljoin(self.base_url, src)
                    images.append(absolute_url)

        except Exception as e:
            logging.error(f"提取产品图片失败: {e}")

        return images

    async def _extract_download_items(self, page) -> List[Dict[str, Any]]:
        """从下载页面提取下载项目"""
        download_items = []

        try:
            # 查找下载项目容器
            item_selectors = [
                'li:has(a[href*=".pdf"], a[href*=".zip"])',
                '.download-item',
                '.file-item',
                'tr:has(a[href*=".pdf"], a[href*=".zip"])'
            ]

            items = []
            for selector in item_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        items = elements
                        break
                except:
                    continue

            for item in items:
                try:
                    # 提取下载链接
                    link_element = await item.query_selector('a[href]')
                    if not link_element:
                        continue

                    href = await link_element.get_attribute('href')
                    link_text = await link_element.inner_text()

                    if not href:
                        continue

                    # 提取文件信息
                    file_info = self.metadata_extractor.extract_from_url(href)

                    # 提取描述信息
                    description_elements = await item.query_selector_all('p, .desc, .description')
                    descriptions = []
                    for desc_elem in description_elements:
                        desc_text = await desc_elem.inner_text()
                        if desc_text and desc_text.strip():
                            descriptions.append(desc_text.strip())

                    download_item = {
                        "url": urljoin(self.base_url, href),
                        "title": link_text.strip() if link_text else file_info["filename"],
                        "filename": file_info["filename"],
                        "file_type": file_info["file_type"],
                        "extension": file_info["extension"],
                        "descriptions": descriptions,
                        "download_type": self._classify_download_type(href, link_text),
                        "found_at": datetime.now().isoformat()
                    }

                    download_items.append(download_item)

                except Exception as e:
                    logging.debug(f"处理下载项目时出错: {e}")
                    continue

        except Exception as e:
            logging.error(f"提取下载项目失败: {e}")

        return download_items

    async def _process_download_resources(self, page):
        """处理所有发现的下载资源"""
        logging.info(f"开始处理 {len(self.download_resources)} 个下载资源")

        # 按类型分组处理
        pdf_resources = [r for r in self.download_resources if r["download_type"] == "pdf"]
        zip_resources = [r for r in self.download_resources if r["download_type"] == "zip"]

        # 并发处理PDF文件
        if pdf_resources:
            logging.info(f"处理 {len(pdf_resources)} 个PDF文件")
            await self._process_pdf_resources(pdf_resources)

        # 并发处理ZIP文件
        if zip_resources:
            logging.info(f"处理 {len(zip_resources)} 个ZIP文件")
            await self._process_zip_resources(zip_resources)

        logging.info("下载资源处理完成")

    async def _process_pdf_resources(self, pdf_resources: List[Dict]):
        """处理PDF资源"""
        semaphore = asyncio.Semaphore(self.download_config["concurrent_downloads"])

        async def process_single_pdf(resource):
            async with semaphore:
                try:
                    logging.info(f"处理PDF: {resource['title']}")

                    result = await self.pdf_processor.download_and_process(
                        resource["url"],
                        self.download_config["download_dir"]
                    )

                    if "error" not in result:
                        # 更新资源信息
                        resource.update({
                            "processed": True,
                            "processing_result": result,
                            "local_file_path": result.get("file_path"),
                            "text_content": result.get("text_content", ""),
                            "page_count": result.get("page_count", 0),
                            "processing_method": result.get("processing_method", "unknown")
                        })

                        # 提取文件元数据
                        if result.get("file_path"):
                            metadata = self.metadata_extractor.extract_from_file(result["file_path"])
                            resource["file_metadata"] = metadata

                        logging.info(f"PDF处理成功: {resource['title']}")
                    else:
                        resource["processing_error"] = result["error"]
                        self.failed_downloads.append({
                            "url": resource["url"],
                            "type": "pdf",
                            "error": result["error"],
                            "timestamp": datetime.now().isoformat()
                        })
                        logging.error(f"PDF处理失败: {resource['title']} - {result['error']}")

                except Exception as e:
                    error_msg = str(e)
                    resource["processing_error"] = error_msg
                    self.failed_downloads.append({
                        "url": resource["url"],
                        "type": "pdf",
                        "error": error_msg,
                        "timestamp": datetime.now().isoformat()
                    })
                    logging.error(f"PDF处理异常: {resource['title']} - {error_msg}")

        # 并发处理所有PDF
        tasks = [process_single_pdf(resource) for resource in pdf_resources]
        await asyncio.gather(*tasks, return_exceptions=True)

    async def _process_zip_resources(self, zip_resources: List[Dict]):
        """处理ZIP资源"""
        semaphore = asyncio.Semaphore(self.download_config["concurrent_downloads"])

        async def process_single_zip(resource):
            async with semaphore:
                try:
                    logging.info(f"处理ZIP: {resource['title']}")

                    result = await self.zip_processor.download_and_process(
                        resource["url"],
                        self.download_config["download_dir"]
                    )

                    if "error" not in result:
                        # 更新资源信息
                        resource.update({
                            "processed": True,
                            "processing_result": result,
                            "local_file_path": result.get("file_path"),
                            "extracted_path": result.get("extracted_path"),
                            "file_list": result.get("file_list", []),
                            "total_files": result.get("total_files", 0),
                            "analysis": result.get("analysis", {})
                        })

                        # 提取文件元数据
                        if result.get("file_path"):
                            metadata = self.metadata_extractor.extract_from_file(result["file_path"])
                            resource["file_metadata"] = metadata

                        logging.info(f"ZIP处理成功: {resource['title']}, 文件数: {result.get('total_files', 0)}")
                    else:
                        resource["processing_error"] = result["error"]
                        self.failed_downloads.append({
                            "url": resource["url"],
                            "type": "zip",
                            "error": result["error"],
                            "timestamp": datetime.now().isoformat()
                        })
                        logging.error(f"ZIP处理失败: {resource['title']} - {result['error']}")

                except Exception as e:
                    error_msg = str(e)
                    resource["processing_error"] = error_msg
                    self.failed_downloads.append({
                        "url": resource["url"],
                        "type": "zip",
                        "error": error_msg,
                        "timestamp": datetime.now().isoformat()
                    })
                    logging.error(f"ZIP处理异常: {resource['title']} - {error_msg}")

        # 并发处理所有ZIP
        tasks = [process_single_zip(resource) for resource in zip_resources]
        await asyncio.gather(*tasks, return_exceptions=True)

    def save_universal_data(self, data: Dict, filename: str = None):
        """保存全站抓取数据"""
        if not filename:
            filename = self.output_config["filenames"]["json"]

        filepath = os.path.join(self.output_config["output_dir"], filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logging.info(f"全站数据已保存到: {filepath}")
        except Exception as e:
            logging.error(f"保存数据失败: {e}")

    def generate_universal_report(self, data: Dict) -> Dict:
        """生成全站抓取报告"""
        report = {
            "summary": {
                "total_pages": len(data.get("pages_data", [])),
                "total_downloads": len(data.get("download_resources", [])),
                "failed_downloads": len(data.get("failed_downloads", [])),
                "discovered_links": len(data.get("discovered_links", [])),
                "scraping_duration": data.get("metadata", {}).get("duration", "unknown")
            },
            "page_types": {},
            "download_types": {},
            "file_analysis": {},
            "success_rates": {}
        }

        try:
            # 统计页面类型
            for page in data.get("pages_data", []):
                page_type = page.get("page_type", "unknown")
                if page_type not in report["page_types"]:
                    report["page_types"][page_type] = 0
                report["page_types"][page_type] += 1

            # 统计下载类型
            for resource in data.get("download_resources", []):
                download_type = resource.get("download_type", "unknown")
                if download_type not in report["download_types"]:
                    report["download_types"][download_type] = 0
                report["download_types"][download_type] += 1

            # 文件分析统计
            pdf_count = len([r for r in data.get("download_resources", []) if r.get("download_type") == "pdf"])
            zip_count = len([r for r in data.get("download_resources", []) if r.get("download_type") == "zip"])
            processed_pdf = len([r for r in data.get("download_resources", []) if r.get("download_type") == "pdf" and r.get("processed")])
            processed_zip = len([r for r in data.get("download_resources", []) if r.get("download_type") == "zip" and r.get("processed")])

            report["file_analysis"] = {
                "pdf_files": pdf_count,
                "zip_files": zip_count,
                "processed_pdf": processed_pdf,
                "processed_zip": processed_zip
            }

            # 成功率统计
            total_downloads = len(data.get("download_resources", []))
            failed_downloads = len(data.get("failed_downloads", []))

            if total_downloads > 0:
                success_rate = ((total_downloads - failed_downloads) / total_downloads) * 100
                report["success_rates"]["overall"] = round(success_rate, 2)

            if pdf_count > 0:
                pdf_success_rate = (processed_pdf / pdf_count) * 100
                report["success_rates"]["pdf"] = round(pdf_success_rate, 2)

            if zip_count > 0:
                zip_success_rate = (processed_zip / zip_count) * 100
                report["success_rates"]["zip"] = round(zip_success_rate, 2)

        except Exception as e:
            logging.error(f"生成报告失败: {e}")
            report["error"] = str(e)

        return report
