@echo off
chcp 65001 >nul
title 全站数据抓取工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    全站数据抓取工具                          ║
echo ║                Universal Web Scraper                         ║
echo ║                                                              ║
echo ║  目标网站: https://www.zhaozhongai.com                       ║
echo ║  开发者: 苏昱                                                ║
echo ║  版本: v1.0                                                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 请选择启动模式:
echo.
echo 1. 🚀 快速启动 (推荐新用户)
echo 2. ⚡ 极速并发模式 (高性能)
echo 3. 🔧 完整功能启动
echo 4. 🧪 运行测试
echo 5. 📊 性能测试
echo 6. 📖 查看使用说明
echo.
set /p choice="请输入选项编号 (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动快速版本...
    python quick_start_universal.py
) else (
    if "%choice%"=="2" (
        echo.
        echo ⚡ 启动极速并发模式...
        python turbo_scraper.py
    ) else (
        if "%choice%"=="3" (
            echo.
            echo 🔧 启动完整版本...
            python main_universal_scraper.py
        ) else (
            if "%choice%"=="4" (
                echo.
                echo 🧪 运行测试...
                python test_universal_scraper.py
            ) else (
                if "%choice%"=="5" (
                    echo.
                    echo 📊 运行性能测试...
                    python performance_test.py
                ) else (
                    if "%choice%"=="6" (
                        echo.
                        echo 📖 使用说明:
                        echo.
                        echo 1. 快速启动: 适合新用户，提供演示功能和基础测试
                        echo 2. 极速并发: 多浏览器并发抓取，速度提升5-10倍
                        echo 3. 完整功能: 提供所有功能，包括全站抓取和资源处理
                        echo 4. 运行测试: 检查所有组件是否正常工作
                        echo 5. 性能测试: 测试不同并发模式的性能表现
                        echo.
                        echo 输出文件说明:
                        echo • ./output/universal_scraping_data.json - 完整抓取数据
                        echo • ./output/universal_scraping_data.csv - CSV格式数据
                        echo • ./output/universal_scraping_analysis.xlsx - Excel分析表
                        echo • ./output/scraping_report.json - 分析报告
                        echo • ./downloads/ - 下载的文件目录
                        echo.
                        echo 更多信息请查看 README.md 文件
                    ) else (
                        echo ❌ 无效选择
                    )
                )
            )
        )
    )
)

echo.
pause
