#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license         http://www.178188.xyz
@lastmodify      2025年7月31日
模块说明: 基于网站分析生成传感器产品分类表格
"""

import pandas as pd

def create_proximity_sensors_table():
    """创建接近传感器产品表"""
    data = [
        {
            '系列名称': 'E2F系列',
            '类型': '电感式接近传感器',
            '检测距离': '1-8mm',
            '输出方式': 'NPN/PNP',
            '特点': '标准型，性价比高',
            '适用场景': '金属物体检测，机械制造',
            '目标客户': '制造业、自动化设备厂商'
        },
        {
            '系列名称': 'E2B系列',
            '类型': '长距离电感式接近传感器',
            '检测距离': '10-15mm',
            '输出方式': 'NPN/PNP',
            '特点': '检测距离远，抗干扰强',
            '适用场景': '远距离金属检测',
            '目标客户': '重工业、大型设备制造'
        },
        {
            '系列名称': 'E2ZE系列',
            '类型': '电感式接近传感器',
            '检测距离': '1-2mm',
            '输出方式': 'NPN/PNP',
            '特点': '单倍/两倍距离型可选',
            '适用场景': '精密检测，小型零件',
            '目标客户': '精密制造、电子行业'
        },
        {
            '系列名称': 'E2K系列',
            '类型': '电容式接近传感器',
            '检测距离': '2-15mm',
            '输出方式': 'NPN/PNP',
            '特点': '可检测各种材质',
            '适用场景': '非金属物体检测，液位检测',
            '目标客户': '包装、食品、化工行业'
        },
        {
            '系列名称': 'EZE系列',
            '类型': '微小型电感式接近传感器',
            '检测距离': '0.8-2mm',
            '输出方式': 'NPN/PNP',
            '特点': '体积小巧，适合狭小空间',
            '适用场景': '精密装配，狭小空间检测',
            '目标客户': '电子制造、精密仪器'
        },
        {
            '系列名称': 'TL系列',
            '类型': '方形接近传感器',
            '检测距离': '2-8mm',
            '输出方式': 'NPN/PNP',
            '特点': '方形外观，安装方便',
            '适用场景': '方形安装位置，工业检测',
            '目标客户': '自动化设备、机械制造'
        }
    ]
    return pd.DataFrame(data)

def create_photoelectric_sensors_table():
    """创建光电传感器产品表"""
    data = [
        {
            '系列名称': 'E3ZC系列背景抑制',
            '类型': '背景抑制光电传感器',
            '检测距离': '0-300mm/0-600mm',
            '输出方式': 'NPN/PNP',
            '特点': '忽略背景干扰，检测精准',
            '适用场景': '复杂背景环境，物料识别',
            '目标客户': '包装行业、物流分拣'
        },
        {
            '系列名称': 'E3ZC系列远距离',
            '类型': '远距离型光电传感器',
            '检测距离': '10-300mm',
            '输出方式': 'NPN/PNP',
            '特点': '检测距离远，响应快',
            '适用场景': '远距离检测，高速检测',
            '目标客户': '传送带检测、物流自动化'
        },
        {
            '系列名称': 'E3T系列',
            '类型': '超薄型光电开关传感器',
            '检测距离': '根据应用而定',
            '输出方式': 'NPN/PNP',
            '特点': '超薄设计，节省空间',
            '适用场景': '空间受限场合，对射检测',
            '目标客户': '精密设备、空间受限应用'
        },
        {
            '系列名称': 'E3JK系列',
            '类型': '方形(大)光电传感器',
            '检测距离': '10-300mm',
            '输出方式': 'NPN/PNP',
            '特点': '大尺寸，检测稳定',
            '适用场景': '大型物体检测，工业应用',
            '目标客户': '重工业、大型设备'
        },
        {
            '系列名称': 'EE系列',
            '类型': '凹槽型光电开关传感器',
            '检测距离': '凹槽宽度内',
            '输出方式': 'NPN/PNP',
            '特点': '结构紧凑，检测精度高',
            '适用场景': '计数、定位、编码',
            '目标客户': '自动化计数、精密定位'
        },
        {
            '系列名称': 'E3FA系列红外光',
            '类型': '红外光光电传感器',
            '检测距离': '10-100mm',
            '输出方式': 'NPN/PNP',
            '特点': '红外光源，抗环境光干扰',
            '适用场景': '一般物体检测',
            '目标客户': '通用工业应用'
        }
    ]
    return pd.DataFrame(data)

def create_laser_sensors_table():
    """创建激光光电传感器产品表"""
    data = [
        {
            '系列名称': 'E3FA系列激光',
            '类型': '激光光电传感器',
            '检测距离': '70mm-30m',
            '输出方式': 'NPN/PNP',
            '特点': '激光光源，精度高，光斑小',
            '适用场景': '精密定位，远距离检测',
            '目标客户': '精密制造、机器人导航、测量设备'
        },
        {
            '系列名称': 'E3ZC系列方形激光',
            '类型': '方形激光光电传感器',
            '检测距离': '0-300mm',
            '输出方式': 'NPN/PNP',
            '特点': '方形外观，激光检测',
            '适用场景': '方形安装，激光检测',
            '目标客户': '精密设备、自动化系统'
        },
        {
            '系列名称': 'E3ZK系列',
            '类型': '方形激光光电传感器',
            '检测距离': '根据型号而定',
            '输出方式': 'NPN/PNP',
            '特点': '激光技术，检测精准',
            '适用场景': '高精度检测应用',
            '目标客户': '精密制造、质量检测'
        }
    ]
    return pd.DataFrame(data)

def create_customer_needs_matrix():
    """创建客户需求匹配矩阵"""
    data = [
        {
            '客户需求场景': '金属零件接近检测',
            '检测对象': '金属物体',
            '距离要求': '1-8mm',
            '环境特点': '一般工业环境',
            '推荐产品': 'E2F系列电感式接近传感器',
            '推荐理由': '专门检测金属，成本低，稳定可靠',
            '适用行业': '机械制造、汽车零部件'
        },
        {
            '客户需求场景': '塑料包装检测',
            '检测对象': '非金属物体',
            '距离要求': '2-15mm',
            '环境特点': '包装生产线',
            '推荐产品': 'E2K系列电容式接近传感器',
            '推荐理由': '可检测各种材质，包括塑料、纸张等',
            '适用行业': '包装、食品、日化'
        },
        {
            '客户需求场景': '传送带物料检测',
            '检测对象': '各种物料',
            '距离要求': '10-300mm',
            '环境特点': '高速运动，可能有背景干扰',
            '推荐产品': 'E3ZC系列背景抑制光电传感器',
            '推荐理由': '忽略背景干扰，检测准确，响应快',
            '适用行业': '物流、分拣、传送系统'
        },
        {
            '客户需求场景': '机器人精密定位',
            '检测对象': '定位目标',
            '距离要求': '70mm-30m',
            '环境特点': '需要高精度',
            '推荐产品': 'E3FA系列激光光电传感器',
            '推荐理由': '激光光源，精度极高，光斑小',
            '适用行业': '机器人、精密制造、测量'
        },
        {
            '客户需求场景': '狭小空间检测',
            '检测对象': '小型零件',
            '距离要求': '0.8-2mm',
            '环境特点': '安装空间受限',
            '推荐产品': 'EZE系列微小型电感式传感器',
            '推荐理由': '体积小巧，适合狭小空间安装',
            '适用行业': '电子制造、精密装配'
        },
        {
            '客户需求场景': '透明物体检测',
            '检测对象': '玻璃、透明塑料',
            '距离要求': '根据对射距离',
            '环境特点': '透明或半透明物体',
            '推荐产品': 'E3T系列超薄型对射式',
            '推荐理由': '对射式设计，可靠检测透明物体',
            '适用行业': '玻璃制造、透明包装'
        },
        {
            '客户需求场景': '高速计数应用',
            '检测对象': '快速移动物体',
            '距离要求': '凹槽宽度内',
            '环境特点': '高频开关，需要精确计数',
            '推荐产品': 'EE系列凹槽型光电传感器',
            '推荐理由': '响应速度快，计数精确，结构紧凑',
            '适用行业': '自动化计数、编码器应用'
        },
        {
            '客户需求场景': '远距离大物体检测',
            '检测对象': '大型物体',
            '距离要求': '10-300mm',
            '环境特点': '检测距离较远',
            '推荐产品': 'E3ZC系列远距离型',
            '推荐理由': '检测距离远，适合大型物体检测',
            '适用行业': '大型设备、重工业'
        }
    ]
    return pd.DataFrame(data)

def create_sales_guide():
    """创建销售指导表"""
    data = [
        {
            '客户问题': '需要检测金属零件',
            '关键确认点': '零件材质、检测距离、安装空间',
            '推荐流程': '1.确认是否金属 2.测量检测距离 3.选择合适系列',
            '话术建议': '针对金属检测，我推荐电感式传感器，它专门为金属检测设计，稳定可靠',
            '注意事项': '确认金属类型，不同金属检测距离可能不同'
        },
        {
            '客户问题': '需要检测包装盒',
            '关键确认点': '包装材质、检测距离、是否透明',
            '推荐流程': '1.确认材质类型 2.评估检测环境 3.选择传感器类型',
            '话术建议': '包装检测建议使用光电传感器或电容式传感器，根据材质选择',
            '注意事项': '透明包装需要特殊考虑，可能需要对射式'
        },
        {
            '客户问题': '精度要求很高',
            '关键确认点': '精度要求、检测距离、环境条件',
            '推荐流程': '1.了解精度需求 2.评估环境干扰 3.推荐激光系列',
            '话术建议': '高精度应用推荐激光传感器，光斑小、精度高、抗干扰能力强',
            '注意事项': '激光传感器成本较高，需要说明性价比'
        },
        {
            '客户问题': '安装空间很小',
            '关键确认点': '具体尺寸限制、检测要求',
            '推荐流程': '1.测量安装空间 2.确认检测需求 3.推荐微小型系列',
            '话术建议': '空间受限推荐微小型传感器，体积小但功能完整',
            '注意事项': '微小型传感器检测距离相对较短'
        }
    ]
    return pd.DataFrame(data)

def save_all_tables():
    """保存所有表格到Excel文件"""
    with pd.ExcelWriter('传感器产品推荐指南.xlsx', engine='openpyxl') as writer:
        # 产品分类表
        proximity_df = create_proximity_sensors_table()
        proximity_df.to_excel(writer, sheet_name='接近传感器产品', index=False)
        
        photoelectric_df = create_photoelectric_sensors_table()
        photoelectric_df.to_excel(writer, sheet_name='光电传感器产品', index=False)
        
        laser_df = create_laser_sensors_table()
        laser_df.to_excel(writer, sheet_name='激光传感器产品', index=False)
        
        # 客户需求匹配矩阵
        needs_matrix = create_customer_needs_matrix()
        needs_matrix.to_excel(writer, sheet_name='客户需求匹配矩阵', index=False)
        
        # 销售指导
        sales_guide = create_sales_guide()
        sales_guide.to_excel(writer, sheet_name='销售指导手册', index=False)
    
    print("传感器产品推荐指南已保存到 '传感器产品推荐指南.xlsx'")
    print("\n包含以下工作表:")
    print("1. 接近传感器产品 - 详细产品信息")
    print("2. 光电传感器产品 - 详细产品信息") 
    print("3. 激光传感器产品 - 详细产品信息")
    print("4. 客户需求匹配矩阵 - 核心推荐工具")
    print("5. 销售指导手册 - 销售话术和流程")

if __name__ == "__main__":
    save_all_tables()
