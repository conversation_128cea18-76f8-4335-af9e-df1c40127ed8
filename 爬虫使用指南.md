# 兆众智能传感器爬虫工具 v2.0 使用指南

## 🚀 功能特性

### 1. **扩大爬取范围**
- ✅ 支持所有传感器产品类别（9大类）
- ✅ 接近传感器、光电传感器、激光光电传感器
- ✅ 激光位移传感器、色标传感器、光纤传感器
- ✅ 微动/行程/限位开关、管道液位传感器、继电器

### 2. **数据完整性提升**
- ✅ 详细规格参数提取（检测距离、输出方式、工作电压等）
- ✅ 产品图片URL获取
- ✅ 价格信息提取（如有）
- ✅ 技术文档链接收集
- ✅ 多维度产品描述

### 3. **性能优化**
- ✅ 智能重试机制（最多3次重试）
- ✅ 随机延迟避免被限制（1-3秒）
- ✅ 多种选择器策略提高成功率
- ✅ 详细日志记录和进度显示
- ✅ 错误处理和异常恢复

### 4. **数据结构化**
- ✅ 按产品类别组织数据
- ✅ 统一规格参数字段名称
- ✅ 智能数据提取和清洗
- ✅ 数据完整度评估
- ✅ 多格式输出（JSON、CSV）

## 📁 文件说明

### 核心文件
- `sensor_scraper.py` - 增强版爬虫主程序
- `data_processor.py` - 增强版数据处理器
- `test_scraper.py` - 功能测试脚本
- `爬虫使用指南.md` - 本使用指南

### 输出文件
- `sensor_products_enhanced.json` - 完整产品数据
- `sensor_products_enhanced.csv` - CSV格式数据
- `scraping_report.json` - 爬取摘要报告
- `scraper.log` - 详细日志文件

## 🛠️ 安装依赖

```bash
# 安装Python依赖
pip install playwright pandas openpyxl

# 安装浏览器
playwright install chromium
```

## 📖 使用方法

### 1. 基础爬取
```bash
# 运行完整爬取（每个类别最多10个产品详情）
python sensor_scraper.py
```

### 2. 功能测试
```bash
# 运行所有功能测试
python test_scraper.py
```

### 3. 数据处理
```bash
# 处理爬取的数据生成表格
python data_processor.py
```

## ⚙️ 配置选项

### 爬取参数调整
在 `sensor_scraper.py` 中可以调整：

```python
# 修改延迟范围（秒）
self.delay_range = (1, 3)

# 修改重试次数
self.retry_count = 3

# 修改每个类别爬取的详情数量
products = await scraper.scrape_all_products(max_products_per_category=10)
```

### 日志级别
```python
# 在脚本开头修改日志级别
logging.basicConfig(level=logging.INFO)  # INFO, DEBUG, WARNING, ERROR
```

## 📊 输出数据格式

### JSON格式结构
```json
{
  "metadata": {
    "scraped_at": "2025-07-31T10:00:00",
    "total_products": 150,
    "categories": ["接近传感器", "光电传感器", ...],
    "scraper_version": "2.0"
  },
  "products": [
    {
      "category": "接近传感器",
      "name": "E2ZE-M08KS01W-N1",
      "url": "https://www.zhaozhongai.com/product/288.html",
      "specifications": {
        "检测距离": "1mm",
        "输出方式": "NPN",
        "工作电压": "12-24VDC"
      },
      "descriptions": ["产品描述..."],
      "image": "图片URL",
      "price": "价格信息",
      "documents": [{"title": "技术手册", "url": "文档链接"}],
      "scraped_at": "2025-07-31T10:00:00"
    }
  ],
  "failed_urls": ["失败的URL列表"]
}
```

### CSV格式字段
- 产品类别、产品名称、产品URL
- 检测距离、输出方式、检测方式、工作电压
- 应用场景、特殊功能、价格、图片URL
- 数据完整度评分

## 🔍 测试功能

### 测试类型
1. **数据处理功能测试** - 验证数据解析和处理
2. **产品详情爬取测试** - 测试单个产品页面爬取
3. **单个类别爬取测试** - 测试完整类别爬取流程
4. **所有类别列表测试** - 测试所有类别的产品列表获取

### 运行测试
```bash
python test_scraper.py
```

测试完成后会生成：
- `test_report.json` - 测试结果报告
- `test_*.json` - 各项测试的数据文件

## 📈 性能特性

### 智能重试机制
- 网络请求失败自动重试
- 指数退避策略避免频繁请求
- 失败URL记录便于后续处理

### 多策略数据提取
- 多种CSS选择器策略
- 正则表达式模式匹配
- 智能文本解析和清洗

### 礼貌性爬取
- 随机延迟避免服务器压力
- 合理的并发控制
- 用户代理和请求头伪装

## 🚨 注意事项

### 使用建议
1. **首次使用**：建议先运行测试脚本验证功能
2. **网络环境**：确保网络连接稳定
3. **爬取频率**：避免过于频繁的爬取，建议间隔使用
4. **数据备份**：重要数据请及时备份

### 常见问题
1. **网络超时**：检查网络连接，可能需要重试
2. **数据不完整**：某些产品页面可能结构特殊，属正常现象
3. **爬取速度慢**：为避免被限制，设置了合理延迟

### 错误处理
- 所有错误都会记录在日志文件中
- 失败的URL会单独记录便于后续处理
- 程序具有良好的异常恢复能力

## 📞 技术支持

如遇到问题，请检查：
1. `scraper.log` 日志文件中的错误信息
2. `test_report.json` 测试报告
3. 网络连接和依赖安装情况

## 🔄 版本更新

### v2.0 新特性
- 扩展到9大产品类别
- 增强数据提取算法
- 添加完整测试套件
- 优化性能和稳定性
- 丰富输出格式和报告

### v1.0 基础功能
- 基础的3类传感器爬取
- 简单的数据提取
- JSON格式输出

---

**开发者**: 苏昱  
**许可证**: http://www.178188.xyz  
**最后更新**: 2025年7月31日
