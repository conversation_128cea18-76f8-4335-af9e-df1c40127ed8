#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试启动器功能
"""

import os
import sys

def test_launcher():
    """测试启动器"""
    print("🧪 测试启动器功能...")
    
    # 检查Python环境
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查必需文件
    required_files = [
        "turbo_scraper.py",
        "quick_start_universal.py", 
        "test_concurrent.py",
        "启动器.py"
    ]
    
    print("\n📁 检查必需文件:")
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")
    
    # 检查目录
    print("\n📂 检查目录结构:")
    directories = ["output", "downloads"]
    for directory in directories:
        if os.path.exists(directory):
            print(f"  ✅ {directory}/")
        else:
            print(f"  📁 {directory}/ (将自动创建)")
    
    print("\n🎉 启动器测试完成！")
    print("\n💡 使用方法:")
    print("  python 启动器.py  # 启动图形化菜单")
    print("  python turbo_scraper.py  # 直接启动极速模式")
    print("  python test_concurrent.py  # 运行功能测试")

if __name__ == "__main__":
    test_launcher()
