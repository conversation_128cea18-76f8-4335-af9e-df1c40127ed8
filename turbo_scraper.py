#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 极速爬虫启动器 - 高性能并发抓取控制台
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from pathlib import Path

# 导入并发爬虫
from concurrent_scraper import ConcurrentUniversalScraper, PerformanceMonitor
from data_exporter import DataExporter
from config import CONCURRENT_CONFIG

def setup_turbo_logging():
    """设置高性能日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('turbo_scraper.log', encoding='utf-8')
        ]
    )

def print_turbo_banner():
    """打印极速爬虫横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 极速全站爬虫 v2.0                      ║
║                  Turbo Universal Web Scraper                ║
║                                                              ║
║  🔥 多浏览器并发抓取 | ⚡ 智能任务分配 | 📊 实时性能监控      ║
║                                                              ║
║  目标网站: https://www.zhaozhongai.com                       ║
║  开发者: 苏昱 | 版本: v2.0 | 更新: 2025-08-01               ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_performance_modes():
    """显示性能模式选择"""
    modes = CONCURRENT_CONFIG["modes"]
    
    print("\n🎯 请选择并发模式:")
    print("=" * 60)
    
    mode_list = [
        ("turbo", "🚀", "极速模式"),
        ("fast", "⚡", "快速模式"), 
        ("normal", "🔧", "标准模式"),
        ("safe", "🛡️", "安全模式")
    ]
    
    for i, (mode_key, icon, name) in enumerate(mode_list, 1):
        mode_config = modes[mode_key]
        print(f"{i}. {icon} {name}")
        print(f"   并发数: {mode_config['max_browsers']} 个浏览器")
        print(f"   延迟: {mode_config['base_delay']}s")
        print(f"   说明: {mode_config['description']}")
        print()
    
    print("5. 🔧 自定义配置")
    print("0. 🚪 退出")
    print("=" * 60)

def get_custom_config():
    """获取自定义配置"""
    print("\n🔧 自定义配置:")
    
    try:
        max_browsers = int(input("请输入并发浏览器数量 (1-20, 推荐5): ") or "5")
        max_browsers = max(1, min(20, max_browsers))
        
        base_delay = float(input("请输入基础延迟时间 (0.1-5.0秒, 推荐0.5): ") or "0.5")
        base_delay = max(0.1, min(5.0, base_delay))
        
        max_pages = int(input("请输入最大抓取页面数 (0=无限制, 推荐0): ") or "0")
        max_pages = max(0, max_pages)
        
        return {
            "mode": "custom",
            "max_browsers": max_browsers,
            "base_delay": base_delay,
            "max_pages": max_pages,
            "description": f"自定义模式 - {max_browsers}浏览器/{base_delay}s延迟"
        }
        
    except ValueError:
        print("❌ 输入无效，使用默认配置")
        return {
            "mode": "fast",
            "max_browsers": 5,
            "base_delay": 0.5,
            "max_pages": 0,
            "description": "快速模式 - 默认配置"
        }

async def run_turbo_scraping(mode_config: dict):
    """运行极速抓取"""
    print(f"\n🚀 启动极速抓取 - {mode_config['description']}")
    print("=" * 60)
    
    try:
        # 创建并发爬虫实例
        if mode_config["mode"] == "custom":
            # 自定义模式需要特殊处理
            scraper = ConcurrentUniversalScraper("fast")  # 基于fast模式
            scraper.max_browsers = mode_config["max_browsers"]
            scraper.base_delay = mode_config["base_delay"]
        else:
            scraper = ConcurrentUniversalScraper(mode_config["mode"])
        
        # 显示配置信息
        print(f"📊 配置信息:")
        print(f"   并发浏览器: {scraper.max_browsers} 个")
        print(f"   基础延迟: {scraper.base_delay}s")
        print(f"   最大页面: {mode_config.get('max_pages', 0)} (0=无限制)")
        print()
        
        # 开始抓取
        start_time = datetime.now()
        print(f"⏰ 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔄 正在抓取数据...")
        print()
        
        # 执行并发抓取
        result_data = await scraper.scrape_concurrent_data(
            max_pages=mode_config.get("max_pages", 0)
        )
        
        # 显示结果
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n✅ 抓取完成!")
        print("=" * 60)
        print(f"⏰ 完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {duration}")
        print(f"📄 抓取页面: {len(result_data.get('pages_data', []))} 个")
        print(f"📁 下载资源: {len(result_data.get('download_resources', []))} 个")
        print(f"❌ 失败下载: {len(result_data.get('failed_downloads', []))} 个")
        
        # 性能统计
        perf_stats = result_data.get("metadata", {}).get("performance_stats", {})
        if perf_stats:
            print(f"🚀 平均速度: {perf_stats.get('speed_per_minute', 0):.1f} 页面/分钟")
            print(f"💾 内存使用: {perf_stats.get('memory_mb', 0):.1f} MB")
            print(f"🔥 CPU使用: {perf_stats.get('cpu_percent', 0):.1f}%")
        
        # 保存数据
        print("\n💾 保存数据...")
        scraper.save_universal_data(result_data, f"turbo_scraping_{mode_config['mode']}_data.json")
        
        # 生成报告
        report = scraper.generate_universal_report(result_data)
        
        # 导出多格式数据
        exporter = DataExporter()
        await exporter.export_all_formats(result_data, report)
        
        print("✅ 数据保存完成!")
        
        # 显示输出文件
        print("\n📁 输出文件:")
        output_dir = Path("./output")
        if output_dir.exists():
            for file in output_dir.glob("*"):
                if file.is_file():
                    file_size = file.stat().st_size / 1024  # KB
                    print(f"  • {file.name} ({file_size:.1f}KB)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 抓取失败: {e}")
        logging.error(f"极速抓取失败: {e}")
        return False

def show_performance_tips():
    """显示性能优化提示"""
    print("\n💡 性能优化提示:")
    print("=" * 60)
    print("🚀 极速模式: 最快速度，适合网络良好环境")
    print("⚡ 快速模式: 平衡速度和稳定性，推荐使用")
    print("🔧 标准模式: 稳定可靠，适合网络一般环境")
    print("🛡️  安全模式: 最大兼容性，适合网络较差环境")
    print()
    print("📊 监控说明:")
    print("• 进度条显示实时抓取进度")
    print("• 内存/CPU使用率监控系统负载")
    print("• 速度统计帮助评估性能")
    print()
    print("⚠️  注意事项:")
    print("• 并发数过高可能被网站限制")
    print("• 建议先用快速模式测试")
    print("• 大量数据抓取需要足够磁盘空间")
    print("=" * 60)

async def main():
    """主函数"""
    setup_turbo_logging()
    
    while True:
        try:
            print_turbo_banner()
            print_performance_modes()
            
            choice = input("请选择模式 (0-5): ").strip()
            
            if choice == '0':
                print("\n👋 感谢使用极速爬虫！")
                break
            elif choice == '1':
                mode_config = {"mode": "turbo", **CONCURRENT_CONFIG["modes"]["turbo"], "max_pages": 0}
            elif choice == '2':
                mode_config = {"mode": "fast", **CONCURRENT_CONFIG["modes"]["fast"], "max_pages": 0}
            elif choice == '3':
                mode_config = {"mode": "normal", **CONCURRENT_CONFIG["modes"]["normal"], "max_pages": 0}
            elif choice == '4':
                mode_config = {"mode": "safe", **CONCURRENT_CONFIG["modes"]["safe"], "max_pages": 0}
            elif choice == '5':
                mode_config = get_custom_config()
            else:
                print("❌ 无效选择，请重新输入")
                continue
            
            # 显示性能提示
            if choice in ['1', '2', '3', '4', '5']:
                show_performance_tips()
                
                confirm = input(f"\n确认使用 {mode_config['description']} 开始抓取? (y/N): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    success = await run_turbo_scraping(mode_config)
                    
                    if success:
                        print("\n🎉 抓取任务完成!")
                    else:
                        print("\n😞 抓取任务失败，请检查日志")
                    
                    input("\n按回车键继续...")
                else:
                    print("❌ 取消抓取")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            logging.error(f"程序异常: {e}")
            print(f"❌ 程序异常: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    asyncio.run(main())
