#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 极速爬虫启动器 - 替代批处理文件的Python启动器
"""

import os
import sys
import subprocess
import platform

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 极速全站爬虫 v2.0                      ║
║                  Turbo Universal Web Scraper                ║
║                                                              ║
║  目标网站: https://www.zhaozhongai.com                       ║
║  开发者: 苏昱 | 版本: v2.0 | 更新: 2025-08-01               ║
║  特性: 多浏览器并发 | 5-10倍速度提升                        ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_environment():
    """检查Python环境"""
    print("正在检查Python环境...")
    
    try:
        python_version = sys.version
        print(f"✅ Python环境检查通过: {python_version.split()[0]}")
        return True
    except Exception as e:
        print(f"❌ Python环境检查失败: {e}")
        return False

def check_required_files():
    """检查必需的文件"""
    required_files = [
        "turbo_scraper.py",
        "quick_start_universal.py", 
        "main_universal_scraper.py",
        "test_concurrent.py",
        "performance_test.py",
        "速度对比演示.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    else:
        print("✅ 所有必需文件检查通过")
        return True

def run_script(script_name, description):
    """运行Python脚本"""
    print(f"\n🚀 启动{description}...")
    print(f"💡 提示: {description}")
    print()
    
    try:
        # 使用当前Python解释器运行脚本
        result = subprocess.run([sys.executable, script_name], 
                              cwd=os.getcwd(),
                              capture_output=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("                        🎯 启动菜单")
    print("="*60)
    print()
    print("1. ⚡ 极速并发模式 (推荐) - 5-10倍速度提升")
    print("2. 🚀 快速启动模式 - 适合新用户体验")
    print("3. 🔧 完整功能模式 - 所有功能完整版")
    print("4. 🧪 功能测试 - 检查组件是否正常")
    print("5. 📊 性能测试 - 测试不同模式性能")
    print("6. 🎭 速度演示 - 对比原版和并发版")
    print("7. 📖 使用说明 - 查看详细说明")
    print("0. 🚪 退出程序")
    print()
    print("="*60)

def show_help():
    """显示帮助信息"""
    print("\n📖 使用说明:")
    print("="*60)
    print()
    print("🎯 模式说明:")
    print("  1. 极速并发: 多浏览器并发抓取，速度提升5-10倍")
    print("  2. 快速启动: 适合新用户，提供演示功能和基础测试")
    print("  3. 完整功能: 提供所有功能，包括全站抓取和资源处理")
    print("  4. 功能测试: 检查所有组件是否正常工作")
    print("  5. 性能测试: 测试不同并发模式的性能表现")
    print("  6. 速度演示: 直观对比原版和并发版的速度差异")
    print()
    print("📁 输出文件说明:")
    print("  • ./output/universal_scraping_data.json - 完整抓取数据")
    print("  • ./output/universal_scraping_data.csv - CSV格式数据")
    print("  • ./output/universal_scraping_analysis.xlsx - Excel分析表")
    print("  • ./downloads/ - 下载的文件目录")
    print()
    print("💡 使用建议:")
    print("  • 新用户建议先选择 '功能测试' 验证环境")
    print("  • 然后选择 '极速并发模式' 体验高性能抓取")
    print("  • 如需了解性能提升效果，可选择 '速度演示'")
    print()
    print("="*60)

def main():
    """主函数"""
    # 设置控制台编码
    if platform.system() == "Windows":
        os.system("chcp 65001 >nul")
    
    while True:
        try:
            print_banner()
            
            # 环境检查
            if not check_python_environment():
                input("\n按回车键退出...")
                return
            
            if not check_required_files():
                input("\n按回车键退出...")
                return
            
            # 显示菜单
            show_menu()
            
            choice = input("请输入选项编号 (0-7): ").strip()
            
            if choice == '0':
                print("\n👋 感谢使用极速全站爬虫！")
                print()
                print("🎉 项目特色:")
                print("  • 5-10倍速度提升的并发抓取")
                print("  • 智能任务分配和负载均衡")
                print("  • 实时性能监控和进度显示")
                print("  • 多种模式适应不同需求")
                print()
                print("📞 技术支持: 如有问题请查看使用指南或联系开发者")
                break
                
            elif choice == '1':
                success = run_script("turbo_scraper.py", "极速并发模式 - 多浏览器并发抓取，速度提升5-10倍")
                
            elif choice == '2':
                success = run_script("quick_start_universal.py", "快速启动模式 - 适合新用户，提供演示功能")
                
            elif choice == '3':
                success = run_script("main_universal_scraper.py", "完整功能模式 - 包含所有功能的完整版本")
                
            elif choice == '4':
                success = run_script("test_concurrent.py", "功能测试 - 检查所有组件是否正常工作")
                
            elif choice == '5':
                success = run_script("performance_test.py", "性能测试 - 测试不同并发模式的性能表现")
                
            elif choice == '6':
                success = run_script("速度对比演示.py", "速度演示 - 对比原版和并发版的速度差异")
                
            elif choice == '7':
                show_help()
                input("\n按回车键继续...")
                continue
                
            else:
                print("❌ 无效选择，请输入 0-7 之间的数字")
                input("\n按回车键继续...")
                continue
            
            # 程序执行完成提示
            if choice in ['1', '2', '3', '4', '5', '6']:
                print("\n" + "="*60)
                print("                        ✅ 程序执行完成")
                print("="*60)
                print()
                print("💡 提示:")
                print("  • 如需查看输出文件，请检查 ./output/ 目录")
                print("  • 如需重新运行，请选择相应的模式")
                print("  • 如遇问题，请选择 '功能测试' 检查环境")
                print()
                
                continue_choice = input("是否返回主菜单? (y/N): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    break
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序异常: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
