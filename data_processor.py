#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年7月31日
模块说明: 传感器产品数据处理和表格生成 - 增强版
"""

import json
import pandas as pd
import re
import logging
from collections import defaultdict
from datetime import datetime
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class EnhancedSensorDataProcessor:
    def __init__(self, json_file="sensor_products_enhanced.json"):
        self.json_file = json_file
        self.products_data = []
        self.metadata = {}
        self.failed_urls = []
        self.load_data()

    def load_data(self):
        """加载JSON数据 - 增强版"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查数据格式
            if isinstance(data, dict) and 'products' in data:
                # 新格式：包含元数据
                self.products_data = data.get('products', [])
                self.metadata = data.get('metadata', {})
                self.failed_urls = data.get('failed_urls', [])
                logging.info(f"加载增强格式数据: {len(self.products_data)} 个产品")
            elif isinstance(data, list):
                # 旧格式：直接是产品列表
                self.products_data = data
                logging.info(f"加载传统格式数据: {len(self.products_data)} 个产品")
            else:
                logging.error("未知的数据格式")
                self.products_data = []

            if self.metadata:
                logging.info(f"数据爬取时间: {self.metadata.get('scraped_at', '未知')}")
                logging.info(f"爬取类别: {self.metadata.get('categories', [])}")

        except FileNotFoundError:
            logging.error(f"文件 {self.json_file} 不存在，请先运行爬虫脚本")
            self.products_data = []
        except json.JSONDecodeError as e:
            logging.error(f"JSON文件格式错误: {e}")
            self.products_data = []
        except Exception as e:
            logging.error(f"加载数据时出错: {e}")
            self.products_data = []
    
    def extract_key_specs(self, product):
        """提取关键规格参数 - 增强版"""
        specs = product.get('specifications', {})
        descriptions = product.get('descriptions', [])

        # 合并所有文本用于提取信息
        all_text = ' '.join(descriptions) + ' ' + ' '.join(str(v) for v in specs.values())

        extracted = {
            '产品型号': product.get('name', ''),
            '产品类别': product.get('category', ''),
            '产品URL': product.get('url', ''),
            '检测距离': '',
            '输出方式': '',
            '检测方式': '',
            '工作电压': '',
            '外形尺寸': '',
            '应用场景': '',
            '特殊功能': '',
            '价格': product.get('price', ''),
            '图片URL': product.get('image', ''),
            '技术文档': '',
            '数据完整度': 0
        }

        # 从规格中直接提取
        spec_mapping = {
            '检测距离': ['检测距离', 'Detection distance', '感应距离'],
            '输出方式': ['输出方式', 'Output', '输出类型'],
            '检测方式': ['检测方式', 'Detection method', '检测类型'],
            '工作电压': ['工作电压', '电源电压', 'Supply voltage', 'Voltage'],
            '外形尺寸': ['外形尺寸', '尺寸', 'Dimensions', 'Size']
        }

        for field, possible_keys in spec_mapping.items():
            for key in possible_keys:
                if key in specs:
                    extracted[field] = specs[key]
                    break

        # 智能文本提取
        patterns = {
            '检测距离': [
                r'检测距离[：:]\s*([^，,\n\r；;]+)',
                r'感应距离[：:]\s*([^，,\n\r；;]+)',
                r'(\d+(?:\.\d+)?(?:mm|cm|m))',
                r'(\d+~\d+(?:mm|cm|m))',
                r'(0~\d+(?:mm|cm|m))',
                r'(\d+(?:\.\d+)?-\d+(?:\.\d+)?(?:mm|cm|m))'
            ],
            '工作电压': [
                r'工作电压[：:]\s*([^，,\n\r；;]+)',
                r'电源电压[：:]\s*([^，,\n\r；;]+)',
                r'(\d+(?:\.\d+)?V(?:DC|AC)?)',
                r'(\d+~\d+V(?:DC|AC)?)',
                r'(\d+-\d+V(?:DC|AC)?)'
            ],
            '输出方式': [
                r'输出方式[：:]\s*([^，,\n\r；;]+)',
                r'输出类型[：:]\s*([^，,\n\r；;]+)',
                r'(NPN|PNP|继电器|Relay)',
                r'(常开|常闭|NO|NC)'
            ]
        }

        for field, field_patterns in patterns.items():
            if not extracted[field]:
                for pattern in field_patterns:
                    matches = re.findall(pattern, all_text, re.IGNORECASE)
                    if matches:
                        extracted[field] = matches[0].strip()
                        break

        # 检测方式智能识别
        if not extracted['检测方式']:
            detection_methods = {
                '电感式': ['电感式', 'Inductive'],
                '电容式': ['电容式', 'Capacitive'],
                '光电': ['光电', 'Photoelectric'],
                '激光': ['激光', 'Laser'],
                '背景抑制': ['背景抑制', 'Background suppression'],
                '扩散反射': ['扩散反射', 'Diffuse'],
                '对射式': ['对射式', 'Through-beam'],
                '凹槽型': ['凹槽型', 'Slot'],
                '色标': ['色标', 'Color mark'],
                '光纤': ['光纤', 'Fiber optic'],
                '位移': ['位移', 'Displacement']
            }

            for method, keywords in detection_methods.items():
                if any(keyword in all_text for keyword in keywords):
                    extracted['检测方式'] = method
                    break

        # 应用场景推断 - 更智能
        scenarios = []
        category = product.get('category', '')

        scenario_mapping = {
            '接近传感器': ['物体接近检测', '位置检测'],
            '光电传感器': ['光电检测', '物体检测'],
            '激光光电传感器': ['精密定位', '激光检测'],
            '激光位移传感器': ['位移测量', '精密测量'],
            '色标传感器': ['色标识别', '包装检测'],
            '光纤传感器': ['光纤检测', '精密检测'],
            '微动/行程/限位开关': ['限位检测', '行程控制'],
            '管道液位传感器': ['液位检测', '液体监测'],
            '继电器': ['信号控制', '电路控制']
        }

        if category in scenario_mapping:
            scenarios.extend(scenario_mapping[category])

        # 根据特征词添加场景
        feature_scenarios = {
            '背景抑制': '复杂背景检测',
            '长距离': '远距离检测',
            '微小型': '狭小空间检测',
            '防水': '恶劣环境检测',
            '高精度': '精密检测',
            '高速': '高速检测'
        }

        for feature, scenario in feature_scenarios.items():
            if feature in all_text:
                scenarios.append(scenario)

        extracted['应用场景'] = ', '.join(list(set(scenarios)))

        # 特殊功能提取
        features = []
        feature_keywords = {
            '防水': ['防水', 'IP67', 'IP68', 'Waterproof'],
            '抗干扰': ['抗干扰', '抗噪声', 'Anti-interference'],
            '高精度': ['高精度', '精密', 'High precision'],
            '超薄设计': ['超薄', '薄型', 'Ultra-thin'],
            '两倍距离': ['两倍距离', '2倍距离', 'Double distance'],
            '可调节': ['可调节', '可调', 'Adjustable'],
            '数字显示': ['数字显示', '显示', 'Digital display'],
            '自诊断': ['自诊断', '故障诊断', 'Self-diagnosis']
        }

        for feature, keywords in feature_keywords.items():
            if any(keyword in all_text for keyword in keywords):
                features.append(feature)

        extracted['特殊功能'] = ', '.join(features)

        # 技术文档链接
        documents = product.get('documents', [])
        if documents:
            doc_titles = [doc.get('title', '') for doc in documents]
            extracted['技术文档'] = '; '.join(doc_titles)

        # 计算数据完整度 (0-100)
        completeness_fields = [
            '检测距离', '输出方式', '检测方式', '工作电压',
            '应用场景', '特殊功能'
        ]
        filled_fields = sum(1 for field in completeness_fields if extracted[field])
        extracted['数据完整度'] = int((filled_fields / len(completeness_fields)) * 100)

        return extracted
    
    def create_product_table(self):
        """创建产品对比表格"""
        if not self.products_data:
            return pd.DataFrame()
        
        processed_data = []
        for product in self.products_data:
            extracted = self.extract_key_specs(product)
            processed_data.append(extracted)
        
        df = pd.DataFrame(processed_data)
        return df
    
    def create_category_summary(self):
        """创建分类汇总表"""
        category_summary = defaultdict(list)
        
        for product in self.products_data:
            category = product.get('category', '未知')
            specs = self.extract_key_specs(product)
            category_summary[category].append(specs)
        
        summary_data = []
        for category, products in category_summary.items():
            # 统计该类别的特点
            distances = [p['检测距离'] for p in products if p['检测距离']]
            output_types = [p['输出方式'] for p in products if p['输出方式']]
            detection_methods = [p['检测方式'] for p in products if p['检测方式']]
            
            summary_data.append({
                '传感器类别': category,
                '产品数量': len(products),
                '主要检测距离': ', '.join(set(distances[:3])),  # 前3个不重复的距离
                '输出方式': ', '.join(set(output_types)),
                '检测方式': ', '.join(set(detection_methods)),
                '典型应用': products[0]['应用场景'] if products else ''
            })
        
        return pd.DataFrame(summary_data)
    
    def create_recommendation_matrix(self):
        """创建客户需求推荐矩阵"""
        recommendations = [
            {
                '客户需求场景': '金属物体接近检测',
                '推荐传感器类型': '接近传感器 - 电感式',
                '推荐型号系列': 'E2F系列、E2ZE系列',
                '检测距离范围': '1-8mm',
                '适用行业': '机械制造、自动化生产线',
                '推荐理由': '专门检测金属物体，抗干扰能力强，稳定可靠'
            },
            {
                '客户需求场景': '非金属物体检测',
                '推荐传感器类型': '接近传感器 - 电容式',
                '推荐型号系列': 'E2K系列',
                '检测距离范围': '2-15mm',
                '适用行业': '包装、食品、化工',
                '推荐理由': '可检测各种材质物体，包括液体、粉末等'
            },
            {
                '客户需求场景': '远距离物体检测',
                '推荐传感器类型': '光电传感器',
                '推荐型号系列': 'E3ZC系列远距离型',
                '检测距离范围': '10-300mm',
                '适用行业': '物流分拣、传送带检测',
                '推荐理由': '检测距离远，响应速度快，适合高速检测'
            },
            {
                '客户需求场景': '精密定位检测',
                '推荐传感器类型': '激光光电传感器',
                '推荐型号系列': 'E3FA系列激光',
                '检测距离范围': '70mm-30m',
                '适用行业': '精密制造、机器人导航',
                '推荐理由': '激光光源，检测精度高，光斑小，定位准确'
            },
            {
                '客户需求场景': '复杂背景环境检测',
                '推荐传感器类型': '光电传感器 - 背景抑制型',
                '推荐型号系列': 'E3ZC系列背景抑制',
                '检测距离范围': '0-600mm',
                '适用行业': '包装检测、物料识别',
                '推荐理由': '能够忽略背景干扰，专注检测目标物体'
            },
            {
                '客户需求场景': '狭小空间检测',
                '推荐传感器类型': '接近传感器 - 微小型',
                '推荐型号系列': 'EZE系列微小型',
                '检测距离范围': '0.8-2mm',
                '适用行业': '电子制造、精密装配',
                '推荐理由': '体积小巧，适合空间受限的应用场合'
            },
            {
                '客户需求场景': '透明物体检测',
                '推荐传感器类型': '光电传感器 - 对射式',
                '推荐型号系列': 'E3T系列超薄型',
                '检测距离范围': '根据对射距离',
                '适用行业': '玻璃制造、透明包装检测',
                '推荐理由': '对射式设计，能可靠检测透明或半透明物体'
            },
            {
                '客户需求场景': '计数和定位',
                '推荐传感器类型': '光电传感器 - 凹槽型',
                '推荐型号系列': 'EE系列、E2E系列',
                '检测距离范围': '凹槽宽度内',
                '适用行业': '自动化计数、编码器应用',
                '推荐理由': '结构紧凑，检测精度高，适合高频开关应用'
            }
        ]
        
        return pd.DataFrame(recommendations)
    
    def save_all_tables(self):
        """保存所有表格到Excel文件"""
        with pd.ExcelWriter('传感器产品分析表.xlsx', engine='openpyxl') as writer:
            # 产品详细对比表
            product_table = self.create_product_table()
            if not product_table.empty:
                product_table.to_excel(writer, sheet_name='产品详细对比', index=False)
            
            # 分类汇总表
            category_summary = self.create_category_summary()
            if not category_summary.empty:
                category_summary.to_excel(writer, sheet_name='产品分类汇总', index=False)
            
            # 客户需求推荐矩阵
            recommendation_matrix = self.create_recommendation_matrix()
            recommendation_matrix.to_excel(writer, sheet_name='客户需求推荐矩阵', index=False)
        
        print("所有表格已保存到 '传感器产品分析表.xlsx'")
    
    def print_summary(self):
        """打印数据摘要"""
        if not self.products_data:
            print("没有数据可分析")
            return
        
        print(f"\n=== 数据分析摘要 ===")
        print(f"总产品数: {len(self.products_data)}")
        
        # 按类别统计
        categories = defaultdict(int)
        for product in self.products_data:
            categories[product.get('category', '未知')] += 1
        
        print("\n按类别统计:")
        for category, count in categories.items():
            print(f"  {category}: {count} 个产品")

def main():
    processor = SensorDataProcessor()
    
    if not processor.products_data:
        print("请先运行 sensor_scraper.py 爬取数据")
        return
    
    print("开始处理数据...")
    
    # 打印摘要
    processor.print_summary()
    
    # 保存所有表格
    processor.save_all_tables()
    
    print("\n数据处理完成！")
    print("生成的文件:")
    print("- 传感器产品分析表.xlsx (包含3个工作表)")
    print("  * 产品详细对比")
    print("  * 产品分类汇总") 
    print("  * 客户需求推荐矩阵")

if __name__ == "__main__":
    main()
