#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license         http://www.178188.xyz
@lastmodify      2025年7月31日
模块说明: 将Markdown文件转换为Word文档
"""

import markdown
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
import re
import os

class MarkdownToWordConverter:
    def __init__(self):
        self.doc = Document()
        
    def add_heading(self, text, level=1):
        """添加标题"""
        # 移除markdown标记
        text = re.sub(r'^#+\s*', '', text)
        text = re.sub(r'[🎯📋💬⚡🔧📞📝✅🎯📊📁]', '', text).strip()
        
        if level == 1:
            heading = self.doc.add_heading(text, 1)
        elif level == 2:
            heading = self.doc.add_heading(text, 2)
        else:
            heading = self.doc.add_heading(text, 3)
        
        return heading
    
    def add_paragraph(self, text):
        """添加段落"""
        if not text.strip():
            return
        
        # 处理粗体文本
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        text = re.sub(r'__(.*?)__', r'\1', text)
        
        # 移除markdown链接格式，保留文本
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)
        
        para = self.doc.add_paragraph(text)
        return para
    
    def add_table_from_markdown(self, lines):
        """从markdown表格创建Word表格"""
        # 解析表格数据
        rows = []
        for line in lines:
            if '|' in line and not line.strip().startswith('|---'):
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                if cells:
                    rows.append(cells)
        
        if not rows:
            return
        
        # 创建表格
        table = self.doc.add_table(rows=len(rows), cols=len(rows[0]))
        table.style = 'Table Grid'
        
        # 填充数据
        for i, row_data in enumerate(rows):
            for j, cell_data in enumerate(row_data):
                if j < len(table.rows[i].cells):
                    table.rows[i].cells[j].text = cell_data
                    # 第一行设为标题样式
                    if i == 0:
                        table.rows[i].cells[j].paragraphs[0].runs[0].bold = True
    
    def add_list_item(self, text, level=0):
        """添加列表项"""
        # 移除markdown标记
        text = re.sub(r'^[\-\*\+]\s*', '', text)
        text = re.sub(r'^\d+\.\s*', '', text)
        
        para = self.doc.add_paragraph(text, style='List Bullet' if level == 0 else 'List Bullet 2')
        return para
    
    def convert_markdown_file(self, md_file, output_file):
        """转换markdown文件为Word文档"""
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            i = 0
            table_lines = []
            in_table = False
            
            while i < len(lines):
                line = lines[i].rstrip()
                
                # 跳过空行
                if not line:
                    if not in_table:
                        self.doc.add_paragraph()
                    i += 1
                    continue
                
                # 检测表格
                if '|' in line and not in_table:
                    in_table = True
                    table_lines = [line]
                elif '|' in line and in_table:
                    table_lines.append(line)
                elif in_table and '|' not in line:
                    # 表格结束
                    self.add_table_from_markdown(table_lines)
                    in_table = False
                    table_lines = []
                    continue
                
                if in_table:
                    i += 1
                    continue
                
                # 处理标题
                if line.startswith('#'):
                    level = len(line) - len(line.lstrip('#'))
                    self.add_heading(line, level)
                
                # 处理列表
                elif line.startswith(('- ', '* ', '+ ')) or re.match(r'^\d+\.\s', line):
                    self.add_list_item(line)
                
                # 处理代码块
                elif line.startswith('```'):
                    # 跳过代码块
                    i += 1
                    code_lines = []
                    while i < len(lines) and not lines[i].startswith('```'):
                        code_lines.append(lines[i].rstrip())
                        i += 1
                    
                    if code_lines:
                        code_text = '\n'.join(code_lines)
                        para = self.doc.add_paragraph(code_text)
                        para.style = 'No Spacing'
                        # 设置等宽字体
                        for run in para.runs:
                            run.font.name = 'Consolas'
                
                # 处理分隔线
                elif line.startswith('---'):
                    self.doc.add_page_break()
                
                # 处理普通段落
                else:
                    self.add_paragraph(line)
                
                i += 1
            
            # 处理最后的表格
            if in_table and table_lines:
                self.add_table_from_markdown(table_lines)
            
            # 保存文档
            self.doc.save(output_file)
            print(f"已转换: {md_file} -> {output_file}")
            
        except Exception as e:
            print(f"转换 {md_file} 时出错: {e}")
    
    def reset_document(self):
        """重置文档"""
        self.doc = Document()

def convert_files():
    """转换指定的markdown文件"""
    converter = MarkdownToWordConverter()
    
    files_to_convert = [
        ('销售快速参考.md', '销售快速参考.docx'),
        ('README.md', 'README.docx')
    ]
    
    for md_file, word_file in files_to_convert:
        if os.path.exists(md_file):
            converter.convert_markdown_file(md_file, word_file)
            converter.reset_document()
        else:
            print(f"文件不存在: {md_file}")
    
    print("\n转换完成！生成的Word文档:")
    for _, word_file in files_to_convert:
        if os.path.exists(word_file):
            print(f"- {word_file}")

if __name__ == "__main__":
    print("开始转换Markdown文件为Word格式...")
    convert_files()
