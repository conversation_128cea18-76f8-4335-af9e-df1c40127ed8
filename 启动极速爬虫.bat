@echo off
chcp 65001 >nul
title 极速全站爬虫 v2.0

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 极速全站爬虫 v2.0                      ║
echo ║                  Turbo Universal Web Scraper                ║
echo ║                                                              ║
echo ║  目标网站: https://www.zhaozhongai.com                       ║
echo ║  开发者: 苏昱 ^| 版本: v2.0 ^| 更新: 2025-08-01               ║
echo ║  特性: 多浏览器并发 ^| 5-10倍速度提升                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请先安装Python 3.7+
    echo.
    echo 💡 安装建议:
    echo    1. 访问 https://www.python.org/downloads/
    echo    2. 下载并安装 Python 3.7 或更高版本
    echo    3. 安装时勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:menu
echo ══════════════════════════════════════════════════════════════
echo                        🎯 启动菜单
echo ══════════════════════════════════════════════════════════════
echo.
echo 1. ⚡ 极速并发模式 (推荐) - 5-10倍速度提升
echo 2. 🚀 快速启动模式 - 适合新用户体验
echo 3. 🔧 完整功能模式 - 所有功能完整版
echo 4. 🧪 功能测试 - 检查组件是否正常
echo 5. 📊 性能测试 - 测试不同模式性能
echo 6. 🎭 速度演示 - 对比原版和并发版
echo 7. 📖 使用说明 - 查看详细说明
echo 0. 🚪 退出程序
echo.
echo ══════════════════════════════════════════════════════════════
set /p choice="请输入选项编号 (0-7): "

if "%choice%"=="0" goto exit
if "%choice%"=="1" goto turbo
if "%choice%"=="2" goto quick
if "%choice%"=="3" goto full
if "%choice%"=="4" goto test
if "%choice%"=="5" goto performance
if "%choice%"=="6" goto demo
if "%choice%"=="7" goto help

echo ❌ 无效选择，请输入 0-7 之间的数字
echo.
pause
goto menu

:turbo
echo.
echo ⚡ 启动极速并发模式...
echo 💡 提示: 多浏览器并发抓取，速度提升5-10倍
echo.
python turbo_scraper.py
goto end

:quick
echo.
echo 🚀 启动快速模式...
echo 💡 提示: 适合新用户，提供演示功能
echo.
python quick_start_universal.py
goto end

:full
echo.
echo 🔧 启动完整功能模式...
echo 💡 提示: 包含所有功能的完整版本
echo.
python main_universal_scraper.py
goto end

:test
echo.
echo 🧪 运行功能测试...
echo 💡 提示: 检查所有组件是否正常工作
echo.
python test_concurrent.py
goto end

:performance
echo.
echo 📊 运行性能测试...
echo 💡 提示: 测试不同并发模式的性能表现
echo.
python performance_test.py
goto end

:demo
echo.
echo 🎭 运行速度演示...
echo 💡 提示: 对比原版和并发版的速度差异
echo.
python 速度对比演示.py
goto end

:help
echo.
echo 📖 使用说明:
echo ══════════════════════════════════════════════════════════════
echo.
echo 🎯 模式说明:
echo   1. 极速并发: 多浏览器并发抓取，速度提升5-10倍
echo   2. 快速启动: 适合新用户，提供演示功能和基础测试
echo   3. 完整功能: 提供所有功能，包括全站抓取和资源处理
echo   4. 功能测试: 检查所有组件是否正常工作
echo   5. 性能测试: 测试不同并发模式的性能表现
echo   6. 速度演示: 直观对比原版和并发版的速度差异
echo.
echo 📁 输出文件说明:
echo   • ./output/universal_scraping_data.json - 完整抓取数据
echo   • ./output/universal_scraping_data.csv - CSV格式数据
echo   • ./output/universal_scraping_analysis.xlsx - Excel分析表
echo   • ./downloads/ - 下载的文件目录
echo.
echo 💡 使用建议:
echo   • 新用户建议先选择 "功能测试" 验证环境
echo   • 然后选择 "极速并发模式" 体验高性能抓取
echo   • 如需了解性能提升效果，可选择 "速度演示"
echo.
echo ══════════════════════════════════════════════════════════════
echo.
pause
goto menu

:end
echo.
echo ══════════════════════════════════════════════════════════════
echo                        ✅ 程序执行完成
echo ══════════════════════════════════════════════════════════════
echo.
echo 💡 提示:
echo   • 如需查看输出文件，请检查 ./output/ 目录
echo   • 如需重新运行，请选择相应的模式
echo   • 如遇问题，请选择 "功能测试" 检查环境
echo.
set /p continue="是否返回主菜单? (y/N): "
if /i "%continue%"=="y" goto menu
if /i "%continue%"=="yes" goto menu

:exit
echo.
echo 👋 感谢使用极速全站爬虫！
echo.
echo 🎉 项目特色:
echo   • 5-10倍速度提升的并发抓取
echo   • 智能任务分配和负载均衡
echo   • 实时性能监控和进度显示
echo   • 多种模式适应不同需求
echo.
echo 📞 技术支持: 如有问题请查看使用指南或联系开发者
echo.
pause
exit /b 0
