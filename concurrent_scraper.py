#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 高性能并发爬虫引擎 - 多浏览器并发抓取
"""

import asyncio
import logging
import time
import psutil
import threading
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple, Any
from concurrent.futures import ThreadPoolExecutor
from playwright.async_api import async_playwright, Browser, Page
from collections import defaultdict, deque
import json

from universal_scraper import UniversalWebScraper
from config import CONCURRENT_CONFIG, BASE_CONFIG, PERFORMANCE_CONFIG

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.processed_count = 0
        self.failed_count = 0
        self.total_count = 0
        self.current_memory = 0
        self.current_cpu = 0
        self.stats_history = []
        self.lock = threading.Lock()
    
    def update_progress(self, processed: int, failed: int, total: int):
        """更新进度"""
        with self.lock:
            self.processed_count = processed
            self.failed_count = failed
            self.total_count = total
            
            # 更新系统资源使用情况
            process = psutil.Process()
            self.current_memory = process.memory_info().rss / 1024 / 1024  # MB
            self.current_cpu = process.cpu_percent()
            
            # 记录历史统计
            elapsed_time = time.time() - self.start_time
            stats = {
                "timestamp": datetime.now().isoformat(),
                "elapsed_time": elapsed_time,
                "processed": processed,
                "failed": failed,
                "total": total,
                "memory_mb": self.current_memory,
                "cpu_percent": self.current_cpu,
                "speed_per_minute": (processed / elapsed_time * 60) if elapsed_time > 0 else 0
            }
            self.stats_history.append(stats)
    
    def get_current_stats(self) -> Dict:
        """获取当前统计信息"""
        with self.lock:
            elapsed_time = time.time() - self.start_time
            progress_percent = (self.processed_count / self.total_count * 100) if self.total_count > 0 else 0
            
            return {
                "elapsed_time": elapsed_time,
                "processed": self.processed_count,
                "failed": self.failed_count,
                "total": self.total_count,
                "progress_percent": progress_percent,
                "memory_mb": self.current_memory,
                "cpu_percent": self.current_cpu,
                "speed_per_minute": (self.processed_count / elapsed_time * 60) if elapsed_time > 0 else 0,
                "estimated_remaining": ((self.total_count - self.processed_count) / (self.processed_count / elapsed_time)) if self.processed_count > 0 and elapsed_time > 0 else 0
            }
    
    def print_progress(self):
        """打印进度信息"""
        stats = self.get_current_stats()
        
        print(f"\r🚀 进度: {stats['processed']}/{stats['total']} "
              f"({stats['progress_percent']:.1f}%) | "
              f"失败: {stats['failed']} | "
              f"速度: {stats['speed_per_minute']:.1f}/分钟 | "
              f"内存: {stats['memory_mb']:.1f}MB | "
              f"CPU: {stats['cpu_percent']:.1f}% | "
              f"预计剩余: {stats['estimated_remaining']:.0f}秒", end="")

class BrowserPool:
    """浏览器池管理器"""
    
    def __init__(self, max_browsers: int = 5, pages_per_browser: int = 2):
        self.max_browsers = max_browsers
        self.pages_per_browser = pages_per_browser
        self.browsers = []
        self.page_pools = []
        self.available_pages = deque()
        self.busy_pages = set()
        self.lock = asyncio.Lock()
        
    async def initialize(self):
        """初始化浏览器池"""
        logging.info(f"初始化浏览器池: {self.max_browsers} 个浏览器, 每个 {self.pages_per_browser} 个页面")
        
        playwright = await async_playwright().start()
        
        for i in range(self.max_browsers):
            try:
                browser = await playwright.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )
                
                pages = []
                for j in range(self.pages_per_browser):
                    page = await browser.new_page()
                    
                    # 设置页面配置
                    await page.set_extra_http_headers({
                        'User-Agent': BASE_CONFIG["user_agent"],
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Connection': 'keep-alive'
                    })
                    
                    await page.set_viewport_size({"width": 1920, "height": 1080})
                    pages.append(page)
                    self.available_pages.append((i, j, page))
                
                self.browsers.append(browser)
                self.page_pools.append(pages)
                
                logging.info(f"浏览器 {i+1} 初始化完成")
                
            except Exception as e:
                logging.error(f"初始化浏览器 {i+1} 失败: {e}")
        
        logging.info(f"浏览器池初始化完成: {len(self.browsers)} 个浏览器, {len(self.available_pages)} 个页面")
    
    async def get_page(self) -> Optional[Tuple[int, int, Page]]:
        """获取可用页面"""
        async with self.lock:
            if self.available_pages:
                page_info = self.available_pages.popleft()
                self.busy_pages.add(page_info)
                return page_info
            return None
    
    async def return_page(self, page_info: Tuple[int, int, Page]):
        """归还页面"""
        async with self.lock:
            if page_info in self.busy_pages:
                self.busy_pages.remove(page_info)
                self.available_pages.append(page_info)
    
    async def close_all(self):
        """关闭所有浏览器"""
        logging.info("关闭浏览器池...")
        
        for browser in self.browsers:
            try:
                await browser.close()
            except Exception as e:
                logging.error(f"关闭浏览器失败: {e}")
        
        self.browsers.clear()
        self.page_pools.clear()
        self.available_pages.clear()
        self.busy_pages.clear()
        
        logging.info("浏览器池已关闭")

class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self, strategy: str = "balanced"):
        self.strategy = strategy
        self.pending_tasks = deque()
        self.processing_tasks = set()
        self.completed_tasks = []
        self.failed_tasks = []
        self.lock = asyncio.Lock()
        
    async def add_tasks(self, tasks: List[Dict]):
        """添加任务"""
        async with self.lock:
            for task in tasks:
                task["id"] = f"{task['type']}_{len(self.pending_tasks)}"
                task["created_at"] = datetime.now().isoformat()
                task["retries"] = 0
                self.pending_tasks.append(task)
    
    async def get_next_task(self) -> Optional[Dict]:
        """获取下一个任务"""
        async with self.lock:
            if self.pending_tasks:
                task = self.pending_tasks.popleft()
                self.processing_tasks.add(task["id"])
                return task
            return None
    
    async def complete_task(self, task: Dict, result: Any = None):
        """完成任务"""
        async with self.lock:
            task["completed_at"] = datetime.now().isoformat()
            task["result"] = result
            self.processing_tasks.discard(task["id"])
            self.completed_tasks.append(task)
    
    async def fail_task(self, task: Dict, error: str):
        """任务失败"""
        async with self.lock:
            task["failed_at"] = datetime.now().isoformat()
            task["error"] = error
            task["retries"] += 1
            self.processing_tasks.discard(task["id"])
            
            # 检查是否需要重试
            max_retries = CONCURRENT_CONFIG["task_distribution"]["max_retries"]
            if task["retries"] < max_retries:
                # 重新加入队列
                self.pending_tasks.append(task)
                logging.info(f"任务 {task['id']} 重试 {task['retries']}/{max_retries}")
            else:
                # 标记为最终失败
                self.failed_tasks.append(task)
                logging.error(f"任务 {task['id']} 最终失败: {error}")
    
    def get_stats(self) -> Dict:
        """获取队列统计"""
        return {
            "pending": len(self.pending_tasks),
            "processing": len(self.processing_tasks),
            "completed": len(self.completed_tasks),
            "failed": len(self.failed_tasks),
            "total": len(self.pending_tasks) + len(self.processing_tasks) + len(self.completed_tasks) + len(self.failed_tasks)
        }

class ConcurrentUniversalScraper(UniversalWebScraper):
    """高性能并发全站爬虫"""
    
    def __init__(self, concurrent_mode: str = "fast"):
        super().__init__()
        
        # 并发配置
        self.concurrent_mode = concurrent_mode
        self.concurrent_config = CONCURRENT_CONFIG["modes"].get(concurrent_mode, CONCURRENT_CONFIG["modes"]["fast"])
        
        # 核心组件
        self.browser_pool = None
        self.task_queue = TaskQueue(CONCURRENT_CONFIG["task_distribution"]["strategy"])
        self.performance_monitor = PerformanceMonitor()
        
        # 并发控制
        self.max_browsers = self.concurrent_config["max_browsers"]
        self.base_delay = self.concurrent_config["base_delay"]
        
        logging.info(f"ConcurrentUniversalScraper 初始化完成 - 模式: {concurrent_mode}")
        logging.info(f"并发配置: {self.max_browsers} 个浏览器, 基础延迟: {self.base_delay}s")
    
    async def dynamic_delay(self):
        """动态延迟计算"""
        if not CONCURRENT_CONFIG["dynamic_delay"]["enabled"]:
            await asyncio.sleep(self.base_delay)
            return
        
        # 根据当前负载调整延迟
        stats = self.performance_monitor.get_current_stats()
        
        # CPU使用率越高，延迟越长
        cpu_factor = min(stats["cpu_percent"] / 100, 1.0)
        
        # 内存使用率越高，延迟越长
        memory_factor = min(stats["memory_mb"] / CONCURRENT_CONFIG["resource_limits"]["max_memory_mb"], 1.0)
        
        # 计算动态延迟
        dynamic_delay = self.base_delay * (1 + cpu_factor * 0.5 + memory_factor * 0.3)
        dynamic_delay = min(dynamic_delay, CONCURRENT_CONFIG["dynamic_delay"]["max_delay"])
        
        await asyncio.sleep(dynamic_delay)

    async def scrape_concurrent_data(self, max_pages: int = 0) -> Dict:
        """并发全站数据抓取主函数"""
        start_time = datetime.now()
        logging.info(f"开始并发全站数据抓取 - {start_time} - 模式: {self.concurrent_mode}")

        try:
            # 1. 初始化浏览器池
            self.browser_pool = BrowserPool(
                max_browsers=self.max_browsers,
                pages_per_browser=CONCURRENT_CONFIG["pages_per_browser"]
            )
            await self.browser_pool.initialize()

            # 2. 发现所有链接 (使用单个浏览器快速发现)
            page_info = await self.browser_pool.get_page()
            if not page_info:
                raise Exception("无法获取浏览器页面")

            _, _, discovery_page = page_info

            start_urls = [
                self.base_url + self.crawl_config["target_sections"]["product"],
                self.base_url + self.crawl_config["target_sections"]["download"]
            ]

            logging.info("🔍 开始链接发现...")
            discovered_links = await self.discover_all_links(discovery_page, start_urls)
            await self.browser_pool.return_page(page_info)

            # 3. 分类链接并创建任务
            categorized_links = self._categorize_links(discovered_links)
            tasks = self._create_tasks_from_links(categorized_links, max_pages)

            # 4. 添加任务到队列
            await self.task_queue.add_tasks(tasks)

            # 5. 启动并发处理
            logging.info(f"🚀 开始并发处理 {len(tasks)} 个任务...")
            await self._process_tasks_concurrently()

            # 6. 处理下载资源
            if self.download_resources:
                logging.info("📁 开始处理下载资源...")
                await self._process_download_resources_concurrent()

            # 7. 生成结果
            end_time = datetime.now()
            duration = end_time - start_time

            result = {
                "metadata": {
                    "scraped_at": end_time.isoformat(),
                    "duration": str(duration),
                    "concurrent_mode": self.concurrent_mode,
                    "max_browsers": self.max_browsers,
                    "total_pages": len(self.all_pages_data),
                    "total_downloads": len(self.download_resources),
                    "failed_downloads": len(self.failed_downloads),
                    "performance_stats": self.performance_monitor.get_current_stats()
                },
                "pages_data": self.all_pages_data,
                "download_resources": self.download_resources,
                "failed_downloads": self.failed_downloads,
                "discovered_links": list(self.discovered_links),
                "task_stats": self.task_queue.get_stats()
            }

            logging.info(f"✅ 并发抓取完成！耗时: {duration}")
            logging.info(f"成功抓取 {len(self.all_pages_data)} 个页面")
            logging.info(f"处理 {len(self.download_resources)} 个下载资源")

            return result

        except Exception as e:
            logging.error(f"并发抓取过程中发生错误: {e}")
            raise
        finally:
            # 清理资源
            if self.browser_pool:
                await self.browser_pool.close_all()

    def _create_tasks_from_links(self, categorized_links: Dict, max_pages: int) -> List[Dict]:
        """从分类链接创建任务"""
        tasks = []

        # 产品类别页面任务
        for url in categorized_links["product_category"]:
            tasks.append({
                "type": "product_category",
                "url": url,
                "priority": 1
            })

        # 产品详情页面任务
        detail_urls = categorized_links["product_detail"]
        if max_pages > 0:
            detail_urls = detail_urls[:max_pages]

        for url in detail_urls:
            tasks.append({
                "type": "product_detail",
                "url": url,
                "priority": 2
            })

        # 下载页面任务
        for url in categorized_links["download"]:
            tasks.append({
                "type": "download",
                "url": url,
                "priority": 3
            })

        logging.info(f"创建任务: 类别页面 {len(categorized_links['product_category'])} 个, "
                    f"产品详情 {len(detail_urls)} 个, "
                    f"下载页面 {len(categorized_links['download'])} 个")

        return tasks

    async def _process_tasks_concurrently(self):
        """并发处理任务"""
        # 启动性能监控
        monitor_task = None
        if CONCURRENT_CONFIG["monitoring"]["enabled"]:
            monitor_task = asyncio.create_task(self._monitor_progress())

        # 创建工作协程
        workers = []
        for i in range(self.max_browsers * CONCURRENT_CONFIG["pages_per_browser"]):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            workers.append(worker)

        # 等待所有工作完成
        await asyncio.gather(*workers, return_exceptions=True)

        # 停止监控
        if monitor_task:
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass

    async def _worker(self, worker_name: str):
        """工作协程"""
        logging.info(f"工作协程 {worker_name} 启动")

        while True:
            # 获取任务
            task = await self.task_queue.get_next_task()
            if not task:
                # 等待一下看是否有新任务
                await asyncio.sleep(0.1)

                # 检查是否还有任务在处理
                stats = self.task_queue.get_stats()
                if stats["pending"] == 0 and stats["processing"] == 0:
                    break
                continue

            # 获取页面
            page_info = await self.browser_pool.get_page()
            if not page_info:
                await asyncio.sleep(0.1)
                continue

            try:
                # 处理任务
                result = await self._process_single_task(task, page_info[2])
                await self.task_queue.complete_task(task, result)

                # 更新进度
                stats = self.task_queue.get_stats()
                self.performance_monitor.update_progress(
                    stats["completed"],
                    stats["failed"],
                    stats["total"]
                )

            except Exception as e:
                await self.task_queue.fail_task(task, str(e))
                logging.error(f"任务处理失败 {task['id']}: {e}")

            finally:
                # 归还页面
                await self.browser_pool.return_page(page_info)

                # 动态延迟
                await self.dynamic_delay()

        logging.info(f"工作协程 {worker_name} 完成")

    async def _process_single_task(self, task: Dict, page: Page) -> Any:
        """处理单个任务"""
        task_type = task["type"]
        url = task["url"]

        if task_type == "product_category":
            return await self._scrape_product_category_page_concurrent(page, url)
        elif task_type == "product_detail":
            return await self._scrape_product_detail_page_concurrent(page, url)
        elif task_type == "download":
            return await self._scrape_download_page_concurrent(page, url)
        else:
            raise ValueError(f"未知任务类型: {task_type}")

    async def _monitor_progress(self):
        """监控进度"""
        interval = CONCURRENT_CONFIG["monitoring"]["progress_interval"]

        while True:
            try:
                await asyncio.sleep(interval)

                # 打印进度
                self.performance_monitor.print_progress()

                # 检查资源限制
                stats = self.performance_monitor.get_current_stats()

                if stats["memory_mb"] > CONCURRENT_CONFIG["resource_limits"]["max_memory_mb"]:
                    logging.warning(f"内存使用过高: {stats['memory_mb']:.1f}MB")

                if stats["cpu_percent"] > CONCURRENT_CONFIG["resource_limits"]["max_cpu_percent"]:
                    logging.warning(f"CPU使用过高: {stats['cpu_percent']:.1f}%")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"进度监控异常: {e}")

    async def _scrape_product_category_page_concurrent(self, page: Page, url: str) -> Dict:
        """并发版本的产品类别页面抓取"""
        if not await self.retry_request(page, url):
            raise Exception(f"无法访问页面: {url}")

        try:
            category_name = await self._extract_category_name(page, url)
            products = await self._extract_product_list_from_page(page, url)

            page_data = {
                "url": url,
                "page_type": "product_category",
                "category_name": category_name,
                "products_count": len(products),
                "products": products,
                "scraped_at": datetime.now().isoformat()
            }

            self.all_pages_data.append(page_data)
            return page_data

        except Exception as e:
            raise Exception(f"抓取产品类别页面失败: {e}")

    async def _scrape_product_detail_page_concurrent(self, page: Page, url: str) -> Dict:
        """并发版本的产品详情页面抓取"""
        if not await self.retry_request(page, url):
            raise Exception(f"无法访问页面: {url}")

        try:
            product_data = {
                "url": url,
                "page_type": "product_detail",
                "scraped_at": datetime.now().isoformat()
            }

            # 提取产品信息
            product_data.update(await self._extract_product_basic_info(page))
            product_data["specifications"] = await self._extract_product_specifications(page)
            product_data["download_links"] = await self._extract_download_links(page)
            product_data["images"] = await self._extract_product_images(page)

            self.all_pages_data.append(product_data)
            return product_data

        except Exception as e:
            raise Exception(f"抓取产品详情页面失败: {e}")

    async def _scrape_download_page_concurrent(self, page: Page, url: str) -> Dict:
        """并发版本的下载页面抓取"""
        if not await self.retry_request(page, url):
            raise Exception(f"无法访问页面: {url}")

        try:
            download_items = await self._extract_download_items(page)

            page_data = {
                "url": url,
                "page_type": "download",
                "download_items_count": len(download_items),
                "download_items": download_items,
                "scraped_at": datetime.now().isoformat()
            }

            self.all_pages_data.append(page_data)
            self.download_resources.extend(download_items)

            return page_data

        except Exception as e:
            raise Exception(f"抓取下载页面失败: {e}")

    async def _process_download_resources_concurrent(self):
        """并发处理下载资源"""
        # 使用现有的并发下载逻辑，但增加更多并发数
        original_concurrent = self.download_config["concurrent_downloads"]

        # 根据并发模式调整下载并发数
        if self.concurrent_mode == "turbo":
            self.download_config["concurrent_downloads"] = 15
        elif self.concurrent_mode == "fast":
            self.download_config["concurrent_downloads"] = 10
        else:
            self.download_config["concurrent_downloads"] = 8

        try:
            # 使用父类的下载处理方法
            await self._process_download_resources(None)  # page参数在下载中不使用
        finally:
            # 恢复原始配置
            self.download_config["concurrent_downloads"] = original_concurrent
