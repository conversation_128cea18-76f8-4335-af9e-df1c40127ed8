#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 资源文件处理器 - PDF、ZIP文件的下载和内容分析
"""

import os
import re
import logging
import mimetypes
import zipfile
import rarfile
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlparse, unquote
import hashlib
from datetime import datetime

# PDF处理库
try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logging.warning("PDF处理库未安装，PDF功能将被禁用")

# 文件类型检测
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    logging.warning("python-magic未安装，将使用基础文件类型检测")

class MetadataExtractor:
    """文件元数据提取器"""
    
    def __init__(self):
        self.supported_types = {
            'pdf': ['.pdf'],
            'zip': ['.zip', '.rar', '.7z'],
            'doc': ['.doc', '.docx'],
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp'],
            'text': ['.txt', '.md', '.csv']
        }
    
    def extract_from_url(self, url: str) -> Dict[str, Any]:
        """从URL提取文件元数据"""
        parsed = urlparse(url)
        filename = os.path.basename(unquote(parsed.path))
        
        if not filename or '.' not in filename:
            filename = "unknown_file"
        
        file_ext = Path(filename).suffix.lower()
        file_type = self._detect_file_type(file_ext)
        
        metadata = {
            "filename": filename,
            "extension": file_ext,
            "file_type": file_type,
            "url": url,
            "extracted_at": datetime.now().isoformat()
        }
        
        return metadata
    
    def extract_from_file(self, file_path: str) -> Dict[str, Any]:
        """从本地文件提取元数据"""
        if not os.path.exists(file_path):
            return {}
        
        file_stat = os.stat(file_path)
        file_path_obj = Path(file_path)
        
        metadata = {
            "filename": file_path_obj.name,
            "extension": file_path_obj.suffix.lower(),
            "file_size": file_stat.st_size,
            "file_size_mb": round(file_stat.st_size / (1024 * 1024), 2),
            "created_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
            "file_path": file_path
        }
        
        # 文件类型检测
        metadata["file_type"] = self._detect_file_type(file_path_obj.suffix.lower())
        
        # 使用magic库进行更精确的类型检测
        if MAGIC_AVAILABLE:
            try:
                mime_type = magic.from_file(file_path, mime=True)
                metadata["mime_type"] = mime_type
            except:
                pass
        
        # 计算文件哈希
        metadata["file_hash"] = self._calculate_file_hash(file_path)
        
        return metadata
    
    def _detect_file_type(self, extension: str) -> str:
        """检测文件类型"""
        for file_type, extensions in self.supported_types.items():
            if extension in extensions:
                return file_type
        return "other"
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logging.error(f"计算文件哈希失败: {e}")
            return ""

class PDFProcessor:
    """PDF文件处理器"""
    
    def __init__(self):
        self.available = PDF_AVAILABLE
        if not self.available:
            logging.warning("PDF处理功能不可用")
    
    async def download_and_process(self, url: str, download_dir: str) -> Dict[str, Any]:
        """下载并处理PDF文件"""
        if not self.available:
            return {"error": "PDF处理功能不可用"}
        
        try:
            # 下载文件
            file_path = await self._download_file(url, download_dir)
            if not file_path:
                return {"error": "下载失败"}
            
            # 处理PDF内容
            result = await self._process_pdf_content(file_path)
            result["file_path"] = file_path
            result["download_url"] = url
            
            return result
            
        except Exception as e:
            logging.error(f"PDF处理失败: {e}")
            return {"error": str(e)}
    
    async def _download_file(self, url: str, download_dir: str) -> Optional[str]:
        """下载文件"""
        try:
            # 创建文件名
            parsed = urlparse(url)
            filename = os.path.basename(unquote(parsed.path))
            if not filename.endswith('.pdf'):
                filename += '.pdf'
            
            file_path = os.path.join(download_dir, "pdf", filename)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 下载文件
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        logging.info(f"PDF下载成功: {file_path}")
                        return file_path
                    else:
                        logging.error(f"PDF下载失败，状态码: {response.status}")
                        return None
                        
        except Exception as e:
            logging.error(f"PDF下载异常: {e}")
            return None
    
    async def _process_pdf_content(self, file_path: str) -> Dict[str, Any]:
        """处理PDF内容"""
        result = {
            "text_content": "",
            "page_count": 0,
            "metadata": {},
            "processing_method": "unknown"
        }
        
        try:
            # 方法1: 使用pdfplumber
            result_plumber = await self._extract_with_pdfplumber(file_path)
            if result_plumber["success"]:
                result.update(result_plumber)
                result["processing_method"] = "pdfplumber"
                return result
            
            # 方法2: 使用PyPDF2
            result_pypdf = await self._extract_with_pypdf2(file_path)
            if result_pypdf["success"]:
                result.update(result_pypdf)
                result["processing_method"] = "PyPDF2"
                return result
            
            logging.warning(f"PDF内容提取失败: {file_path}")
            
        except Exception as e:
            logging.error(f"PDF处理异常: {e}")
            result["error"] = str(e)
        
        return result
    
    async def _extract_with_pdfplumber(self, file_path: str) -> Dict[str, Any]:
        """使用pdfplumber提取PDF内容"""
        try:
            with pdfplumber.open(file_path) as pdf:
                text_content = ""
                page_count = len(pdf.pages)
                
                # 提取文本内容
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
                
                # 提取元数据
                metadata = pdf.metadata or {}
                
                return {
                    "success": True,
                    "text_content": text_content.strip(),
                    "page_count": page_count,
                    "metadata": dict(metadata),
                    "has_text": bool(text_content.strip())
                }
                
        except Exception as e:
            logging.debug(f"pdfplumber提取失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _extract_with_pypdf2(self, file_path: str) -> Dict[str, Any]:
        """使用PyPDF2提取PDF内容"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = ""
                page_count = len(pdf_reader.pages)
                
                # 提取文本内容
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
                
                # 提取元数据
                metadata = pdf_reader.metadata or {}
                
                return {
                    "success": True,
                    "text_content": text_content.strip(),
                    "page_count": page_count,
                    "metadata": dict(metadata),
                    "has_text": bool(text_content.strip())
                }
                
        except Exception as e:
            logging.debug(f"PyPDF2提取失败: {e}")
            return {"success": False, "error": str(e)}

class ZIPProcessor:
    """ZIP文件处理器"""
    
    def __init__(self):
        self.supported_formats = ['.zip', '.rar']
        
    async def download_and_process(self, url: str, download_dir: str) -> Dict[str, Any]:
        """下载并处理ZIP文件"""
        try:
            # 下载文件
            file_path = await self._download_file(url, download_dir)
            if not file_path:
                return {"error": "下载失败"}
            
            # 处理ZIP内容
            result = await self._process_zip_content(file_path, download_dir)
            result["file_path"] = file_path
            result["download_url"] = url
            
            return result
            
        except Exception as e:
            logging.error(f"ZIP处理失败: {e}")
            return {"error": str(e)}
    
    async def _download_file(self, url: str, download_dir: str) -> Optional[str]:
        """下载ZIP文件"""
        try:
            # 创建文件名
            parsed = urlparse(url)
            filename = os.path.basename(unquote(parsed.path))
            
            # 确保有扩展名
            if not any(filename.lower().endswith(ext) for ext in self.supported_formats):
                filename += '.zip'
            
            file_path = os.path.join(download_dir, "zip", filename)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 下载文件
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        logging.info(f"ZIP下载成功: {file_path}")
                        return file_path
                    else:
                        logging.error(f"ZIP下载失败，状态码: {response.status}")
                        return None
                        
        except Exception as e:
            logging.error(f"ZIP下载异常: {e}")
            return None

    async def _process_zip_content(self, file_path: str, download_dir: str) -> Dict[str, Any]:
        """处理ZIP文件内容"""
        result = {
            "file_list": [],
            "total_files": 0,
            "total_size": 0,
            "extracted_path": "",
            "analysis": {}
        }

        try:
            file_ext = Path(file_path).suffix.lower()

            if file_ext == '.zip':
                result = await self._process_zip_file(file_path, download_dir)
            elif file_ext == '.rar':
                result = await self._process_rar_file(file_path, download_dir)
            else:
                result["error"] = f"不支持的压缩格式: {file_ext}"

        except Exception as e:
            logging.error(f"ZIP内容处理异常: {e}")
            result["error"] = str(e)

        return result

    async def _process_zip_file(self, file_path: str, download_dir: str) -> Dict[str, Any]:
        """处理ZIP文件"""
        result = {
            "file_list": [],
            "total_files": 0,
            "total_size": 0,
            "extracted_path": "",
            "analysis": {}
        }

        try:
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                # 获取文件列表
                file_list = []
                total_size = 0

                for file_info in zip_ref.filelist:
                    file_data = {
                        "filename": file_info.filename,
                        "file_size": file_info.file_size,
                        "compressed_size": file_info.compress_size,
                        "date_time": datetime(*file_info.date_time).isoformat(),
                        "is_dir": file_info.is_dir()
                    }
                    file_list.append(file_data)
                    total_size += file_info.file_size

                result["file_list"] = file_list
                result["total_files"] = len([f for f in file_list if not f["is_dir"]])
                result["total_size"] = total_size

                # 解压文件
                extract_path = os.path.join(download_dir, "extracted", Path(file_path).stem)
                os.makedirs(extract_path, exist_ok=True)

                zip_ref.extractall(extract_path)
                result["extracted_path"] = extract_path

                # 分析文件内容
                result["analysis"] = await self._analyze_extracted_files(extract_path)

                logging.info(f"ZIP解压完成: {extract_path}, 文件数: {result['total_files']}")

        except Exception as e:
            logging.error(f"ZIP文件处理失败: {e}")
            result["error"] = str(e)

        return result

    async def _process_rar_file(self, file_path: str, download_dir: str) -> Dict[str, Any]:
        """处理RAR文件"""
        result = {
            "file_list": [],
            "total_files": 0,
            "total_size": 0,
            "extracted_path": "",
            "analysis": {}
        }

        try:
            with rarfile.RarFile(file_path, 'r') as rar_ref:
                # 获取文件列表
                file_list = []
                total_size = 0

                for file_info in rar_ref.infolist():
                    file_data = {
                        "filename": file_info.filename,
                        "file_size": file_info.file_size,
                        "compressed_size": file_info.compress_size,
                        "date_time": file_info.date_time.isoformat() if file_info.date_time else "",
                        "is_dir": file_info.is_dir()
                    }
                    file_list.append(file_data)
                    total_size += file_info.file_size

                result["file_list"] = file_list
                result["total_files"] = len([f for f in file_list if not f["is_dir"]])
                result["total_size"] = total_size

                # 解压文件
                extract_path = os.path.join(download_dir, "extracted", Path(file_path).stem)
                os.makedirs(extract_path, exist_ok=True)

                rar_ref.extractall(extract_path)
                result["extracted_path"] = extract_path

                # 分析文件内容
                result["analysis"] = await self._analyze_extracted_files(extract_path)

                logging.info(f"RAR解压完成: {extract_path}, 文件数: {result['total_files']}")

        except Exception as e:
            logging.error(f"RAR文件处理失败: {e}")
            result["error"] = str(e)

        return result

    async def _analyze_extracted_files(self, extract_path: str) -> Dict[str, Any]:
        """分析解压后的文件"""
        analysis = {
            "file_types": {},
            "key_files": [],
            "documentation": [],
            "images": [],
            "data_files": []
        }

        try:
            for root, dirs, files in os.walk(extract_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = Path(file).suffix.lower()

                    # 统计文件类型
                    if file_ext in analysis["file_types"]:
                        analysis["file_types"][file_ext] += 1
                    else:
                        analysis["file_types"][file_ext] = 1

                    # 分类重要文件
                    if file_ext in ['.pdf', '.doc', '.docx', '.txt', '.md']:
                        analysis["documentation"].append({
                            "filename": file,
                            "path": file_path,
                            "size": os.path.getsize(file_path)
                        })
                    elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                        analysis["images"].append({
                            "filename": file,
                            "path": file_path,
                            "size": os.path.getsize(file_path)
                        })
                    elif file_ext in ['.csv', '.json', '.xml', '.xlsx']:
                        analysis["data_files"].append({
                            "filename": file,
                            "path": file_path,
                            "size": os.path.getsize(file_path)
                        })

                    # 识别关键文件
                    if any(keyword in file.lower() for keyword in ['readme', 'manual', 'guide', 'spec', 'datasheet']):
                        analysis["key_files"].append({
                            "filename": file,
                            "path": file_path,
                            "type": "documentation"
                        })

        except Exception as e:
            logging.error(f"文件分析失败: {e}")
            analysis["error"] = str(e)

        return analysis
