#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license         http://www.178188.xyz
@lastmodify      2025年7月31日
模块说明: 爬虫功能测试脚本
"""

import asyncio
import json
import logging
from sensor_scraper import EnhancedSensorScraper

# 配置测试日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def test_single_category():
    """测试单个类别的爬取"""
    logging.info("=== 测试单个类别爬取 ===")
    
    scraper = EnhancedSensorScraper()
    
    # 只测试接近传感器类别
    test_categories = {"接近传感器": "/jiejinchuanganqi/"}
    scraper.sensor_categories = test_categories
    
    try:
        products = await scraper.scrape_all_products(max_products_per_category=3)
        
        if products:
            logging.info(f"测试成功！获取到 {len(products)} 个产品")
            
            # 显示第一个产品的详细信息
            first_product = products[0]
            logging.info("第一个产品信息:")
            logging.info(f"  名称: {first_product.get('name')}")
            logging.info(f"  类别: {first_product.get('category')}")
            logging.info(f"  规格数量: {len(first_product.get('specifications', {}))}")
            logging.info(f"  描述数量: {len(first_product.get('descriptions', []))}")
            
            # 保存测试数据
            scraper.save_data("test_single_category.json")
            return True
        else:
            logging.error("测试失败：未获取到产品数据")
            return False
            
    except Exception as e:
        logging.error(f"测试失败: {e}")
        return False

async def test_product_details():
    """测试产品详情爬取"""
    logging.info("=== 测试产品详情爬取 ===")
    
    scraper = EnhancedSensorScraper()
    
    # 创建一个测试产品
    test_product = {
        "category": "测试类别",
        "name": "E2ZE-M08KS01W-N1",
        "url": "https://www.zhaozhongai.com/product/288.html",
        "descriptions": [],
        "image": "",
        "specifications": {}
    }
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            # 设置用户代理
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            # 测试产品详情爬取
            result = await scraper.scrape_product_details(page, test_product)
            
            await browser.close()
            
            if result.get('specifications'):
                logging.info("产品详情爬取测试成功！")
                logging.info(f"获取到规格: {result['specifications']}")
                return True
            else:
                logging.warning("产品详情爬取测试：未获取到规格信息")
                return False
                
    except Exception as e:
        logging.error(f"产品详情测试失败: {e}")
        return False

async def test_all_categories_list():
    """测试所有类别的产品列表爬取（不爬详情）"""
    logging.info("=== 测试所有类别产品列表 ===")
    
    scraper = EnhancedSensorScraper()
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            all_products = []
            
            for category_name, category_url in scraper.sensor_categories.items():
                try:
                    logging.info(f"测试类别: {category_name}")
                    products = await scraper.scrape_product_list(page, category_name, category_url)
                    all_products.extend(products)
                    logging.info(f"  获取到 {len(products)} 个产品")
                    
                    if products:
                        # 显示第一个产品信息
                        first = products[0]
                        logging.info(f"  示例产品: {first.get('name')}")
                    
                except Exception as e:
                    logging.error(f"  类别 {category_name} 测试失败: {e}")
                    continue
            
            await browser.close()
            
            logging.info(f"所有类别测试完成，总共获取 {len(all_products)} 个产品")
            
            # 保存测试结果
            with open("test_all_categories.json", 'w', encoding='utf-8') as f:
                json.dump(all_products, f, ensure_ascii=False, indent=2)
            
            return len(all_products) > 0
            
    except Exception as e:
        logging.error(f"所有类别测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    logging.info("=== 测试数据处理功能 ===")
    
    try:
        # 创建测试数据
        test_data = [
            {
                "category": "接近传感器",
                "name": "E2ZE-M08KS01W-N1",
                "specifications": {
                    "检测距离": "1mm",
                    "输出方式": "NPN",
                    "工作电压": "12-24VDC"
                },
                "descriptions": ["电感式接近传感器", "单倍距离型"],
                "image": "http://example.com/image.jpg",
                "price": "￥120"
            },
            {
                "category": "光电传感器",
                "name": "E3ZC-BN03-R",
                "specifications": {
                    "检测距离": "0~300mm",
                    "检测方式": "背景抑制",
                    "输出方式": "NPN"
                },
                "descriptions": ["背景抑制光电传感器"],
                "image": "http://example.com/image2.jpg"
            }
        ]
        
        scraper = EnhancedSensorScraper()
        scraper.products_data = test_data
        
        # 测试摘要生成
        summary = scraper.generate_summary()
        logging.info("数据摘要生成成功:")
        logging.info(json.dumps(summary, ensure_ascii=False, indent=2))
        
        # 测试数据保存
        scraper.save_data("test_data_processing.json")
        
        # 测试CSV导出
        scraper.export_to_csv("test_data_processing.csv")
        
        logging.info("数据处理功能测试成功！")
        return True
        
    except Exception as e:
        logging.error(f"数据处理测试失败: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    logging.info("开始运行爬虫功能测试...")
    
    tests = [
        ("数据处理功能", test_data_processing),
        ("产品详情爬取", test_product_details),
        ("单个类别爬取", test_single_category),
        ("所有类别列表", test_all_categories_list),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"运行测试: {test_name}")
        logging.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results[test_name] = "通过" if result else "失败"
            logging.info(f"测试 {test_name}: {'通过' if result else '失败'}")
            
        except Exception as e:
            results[test_name] = f"错误: {e}"
            logging.error(f"测试 {test_name} 发生错误: {e}")
    
    # 输出测试结果摘要
    logging.info(f"\n{'='*50}")
    logging.info("测试结果摘要:")
    logging.info(f"{'='*50}")
    
    for test_name, result in results.items():
        logging.info(f"{test_name}: {result}")
    
    # 保存测试报告
    with open("test_report.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logging.info("\n测试完成！生成文件:")
    logging.info("- test_report.json (测试报告)")
    logging.info("- test_*.json (各项测试数据)")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
