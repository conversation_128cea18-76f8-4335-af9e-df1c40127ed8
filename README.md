# 兆众智能传感器产品分析工具

## 功能说明
本工具用于自动化采集和分析兆众智能传感器产品信息，生成客户需求匹配表，帮助销售人员快速推荐合适的产品。

## 文件说明

### 1. sensor_scraper.py - 数据爬取脚本
- 自动爬取兆众智能网站的传感器产品信息
- 支持三大类传感器：接近传感器、光电传感器、激光光电传感器
- 提取产品型号、规格参数、应用场景等关键信息
- 输出JSON格式的结构化数据

### 2. data_processor.py - 数据处理脚本  
- 处理爬取的JSON数据
- 生成Excel表格，包含：
  - 产品详细对比表
  - 产品分类汇总表
  - 客户需求推荐矩阵

## 使用步骤

### 第一步：安装依赖
```bash
pip install playwright pandas openpyxl
playwright install chromium
```

### 第二步：爬取数据
```bash
python sensor_scraper.py
```
执行后会生成 `sensor_products.json` 文件

### 第三步：生成表格
```bash
python data_processor.py
```
执行后会生成 `传感器产品分析表.xlsx` 文件

## 输出文件说明

### sensor_products.json
包含所有爬取的原始产品数据，格式如下：
```json
{
  "category": "接近传感器",
  "name": "E2ZE-M08KS01W-N1", 
  "specifications": {
    "检测距离": "1mm",
    "输出方式": "NPN"
  },
  "descriptions": ["产品描述信息"],
  "url": "产品详情页链接"
}
```

### 传感器产品分析表.xlsx
包含3个工作表：

#### 1. 产品详细对比
- 所有产品的详细参数对比
- 包含型号、类别、检测距离、输出方式等关键信息

#### 2. 产品分类汇总
- 按传感器类别统计产品数量和特点
- 便于了解各类别产品的整体情况

#### 3. 客户需求推荐矩阵 ⭐
- **核心功能**：根据客户需求场景推荐合适产品
- 包含8种常见应用场景的推荐方案
- 每个场景包含推荐产品、适用行业、推荐理由

## 客户需求推荐矩阵使用指南

### 常见客户需求场景：

1. **金属物体接近检测** → 推荐电感式接近传感器
2. **非金属物体检测** → 推荐电容式接近传感器  
3. **远距离物体检测** → 推荐光电传感器
4. **精密定位检测** → 推荐激光光电传感器
5. **复杂背景环境检测** → 推荐背景抑制型光电传感器
6. **狭小空间检测** → 推荐微小型接近传感器
7. **透明物体检测** → 推荐对射式光电传感器
8. **计数和定位** → 推荐凹槽型光电传感器

### 销售话术建议：

**客户咨询时的标准流程：**
1. 了解客户的检测对象（金属/非金属/透明等）
2. 确认检测距离要求
3. 了解安装环境（空间大小、背景复杂度）
4. 确认输出方式需求（NPN/PNP）
5. 根据推荐矩阵匹配合适产品

**推荐话术模板：**
"根据您的应用需求，我推荐XX系列传感器，因为它具有XX特点，特别适合您的XX应用场景，能够有效解决XX问题。"

## 注意事项

1. 爬虫脚本会自动添加延迟，避免对服务器造成压力
2. 如果网站结构发生变化，可能需要调整选择器
3. 建议定期更新数据以获取最新产品信息
4. 推荐矩阵基于产品特性分析，实际应用时请结合具体需求

## 技术支持

如有问题请联系开发者或查看代码注释。
