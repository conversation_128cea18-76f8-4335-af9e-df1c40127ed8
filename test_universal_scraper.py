#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 全站数据抓取工具测试脚本
"""

import asyncio
import unittest
import tempfile
import os
import json
from pathlib import Path
import logging

# 导入测试模块
from universal_scraper import UniversalWebScraper
from resource_processors import PDFProcessor, ZIPProcessor, MetadataExtractor
from data_exporter import DataExporter

# 设置测试日志
logging.basicConfig(level=logging.INFO)

class TestUniversalScraper(unittest.TestCase):
    """全站爬虫测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.scraper = UniversalWebScraper()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_link_filtering(self):
        """测试链接过滤功能"""
        test_links = {
            "https://www.zhaozhongai.com/product/123.html",
            "https://www.zhaozhongai.com/download/",
            "https://www.zhaozhongai.com/contact/",
            "https://www.example.com/test.html",
            "javascript:void(0)",
            "mailto:<EMAIL>"
        }
        
        valid_links = self.scraper._filter_links(test_links, "https://www.zhaozhongai.com")
        
        # 应该包含产品和下载页面
        self.assertIn("https://www.zhaozhongai.com/product/123.html", valid_links)
        self.assertIn("https://www.zhaozhongai.com/download/", valid_links)
        
        # 应该排除联系页面、外部链接、JavaScript和邮件链接
        self.assertNotIn("https://www.zhaozhongai.com/contact/", valid_links)
        self.assertNotIn("https://www.example.com/test.html", valid_links)
        self.assertNotIn("javascript:void(0)", valid_links)
        self.assertNotIn("mailto:<EMAIL>", valid_links)
    
    def test_page_type_classification(self):
        """测试页面类型分类"""
        test_cases = [
            ("https://www.zhaozhongai.com/product/123.html", "product_detail"),
            ("https://www.zhaozhongai.com/jiejinchuanganqi/", "product_category"),
            ("https://www.zhaozhongai.com/download/", "download"),
            ("https://www.zhaozhongai.com", "homepage"),
            ("https://www.zhaozhongai.com/about/", "other")
        ]
        
        for url, expected_type in test_cases:
            result = self.scraper.classify_page_type(url)
            self.assertEqual(result, expected_type, f"URL {url} 分类错误")
    
    def test_download_type_classification(self):
        """测试下载类型分类"""
        test_cases = [
            ("test.pdf", "PDF文档", "pdf"),
            ("data.zip", "压缩包", "zip"),
            ("manual.rar", "说明书", "zip"),
            ("config.txt", "立即下载", "download"),
            ("image.jpg", "图片", "other")
        ]
        
        for href, text, expected_type in test_cases:
            result = self.scraper._classify_download_type(href, text)
            self.assertEqual(result, expected_type, f"下载类型 {href} 分类错误")

class TestResourceProcessors(unittest.TestCase):
    """资源处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.metadata_extractor = MetadataExtractor()
        self.pdf_processor = PDFProcessor()
        self.zip_processor = ZIPProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_metadata_extraction_from_url(self):
        """测试从URL提取元数据"""
        test_url = "https://www.zhaozhongai.com/downloads/manual.pdf"
        metadata = self.metadata_extractor.extract_from_url(test_url)
        
        self.assertEqual(metadata["filename"], "manual.pdf")
        self.assertEqual(metadata["extension"], ".pdf")
        self.assertEqual(metadata["file_type"], "pdf")
        self.assertEqual(metadata["url"], test_url)
        self.assertIn("extracted_at", metadata)
    
    def test_file_type_detection(self):
        """测试文件类型检测"""
        test_cases = [
            (".pdf", "pdf"),
            (".zip", "zip"),
            (".rar", "zip"),
            (".jpg", "image"),
            (".txt", "text"),
            (".unknown", "other")
        ]
        
        for extension, expected_type in test_cases:
            result = self.metadata_extractor._detect_file_type(extension)
            self.assertEqual(result, expected_type, f"文件类型 {extension} 检测错误")
    
    def test_pdf_processor_availability(self):
        """测试PDF处理器可用性"""
        self.assertIsInstance(self.pdf_processor.available, bool)
        if not self.pdf_processor.available:
            logging.warning("PDF处理器不可用，跳过相关测试")
    
    def test_zip_processor_formats(self):
        """测试ZIP处理器支持的格式"""
        expected_formats = ['.zip', '.rar']
        self.assertEqual(self.zip_processor.supported_formats, expected_formats)

class TestDataExporter(unittest.TestCase):
    """数据导出器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        # 临时修改输出目录
        import config
        self.original_output_dir = config.OUTPUT_CONFIG["output_dir"]
        config.OUTPUT_CONFIG["output_dir"] = self.temp_dir
        
        self.exporter = DataExporter()
        
        # 创建测试数据
        self.test_data = {
            "metadata": {
                "scraped_at": "2025-08-01T10:00:00",
                "total_pages": 2,
                "total_downloads": 1,
                "failed_downloads": 0
            },
            "pages_data": [
                {
                    "url": "https://www.zhaozhongai.com/product/123.html",
                    "page_type": "product_detail",
                    "name": "测试传感器",
                    "model": "TEST-001",
                    "descriptions": ["测试产品描述"],
                    "specifications": {"检测距离": "5mm", "输出方式": "NPN"},
                    "download_links": [],
                    "images": [],
                    "scraped_at": "2025-08-01T10:00:00"
                }
            ],
            "download_resources": [
                {
                    "url": "https://www.zhaozhongai.com/test.pdf",
                    "title": "测试PDF",
                    "filename": "test.pdf",
                    "file_type": "pdf",
                    "download_type": "pdf",
                    "processed": True,
                    "page_count": 5,
                    "text_content": "测试内容"
                }
            ],
            "failed_downloads": []
        }
        
        self.test_report = {
            "summary": {
                "total_pages": 2,
                "total_downloads": 1,
                "failed_downloads": 0,
                "scraping_duration": "00:05:30"
            },
            "page_types": {"product_detail": 1, "product_category": 1},
            "download_types": {"pdf": 1},
            "success_rates": {"overall": 100.0, "pdf": 100.0}
        }
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        import config
        
        # 恢复原始配置
        config.OUTPUT_CONFIG["output_dir"] = self.original_output_dir
        
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    async def test_json_export(self):
        """测试JSON导出"""
        await self.exporter.export_json(self.test_data, self.test_report)
        
        # 检查文件是否创建
        json_file = Path(self.temp_dir) / "universal_scraping_data.json"
        report_file = Path(self.temp_dir) / "scraping_report.json"
        
        self.assertTrue(json_file.exists())
        self.assertTrue(report_file.exists())
        
        # 检查文件内容
        with open(json_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        self.assertEqual(loaded_data["metadata"]["total_pages"], 2)
        self.assertEqual(len(loaded_data["pages_data"]), 1)
    
    async def test_csv_export(self):
        """测试CSV导出"""
        await self.exporter.export_csv(self.test_data)
        
        # 检查文件是否创建
        csv_file = Path(self.temp_dir) / "universal_scraping_data.csv"
        self.assertTrue(csv_file.exists())
        
        # 检查文件内容
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            content = f.read()
            self.assertIn("测试传感器", content)
            self.assertIn("TEST-001", content)

async def run_integration_test():
    """运行集成测试"""
    print("🧪 开始集成测试...")
    
    try:
        # 创建爬虫实例
        scraper = UniversalWebScraper()
        
        # 测试基本功能
        print("✅ 爬虫实例创建成功")
        
        # 测试链接过滤
        test_links = {
            "https://www.zhaozhongai.com/product/123.html",
            "https://www.zhaozhongai.com/contact/"
        }
        filtered = scraper._filter_links(test_links, "https://www.zhaozhongai.com")
        print(f"✅ 链接过滤测试通过，过滤后链接数: {len(filtered)}")
        
        # 测试页面分类
        page_type = scraper.classify_page_type("https://www.zhaozhongai.com/product/123.html")
        assert page_type == "product_detail"
        print("✅ 页面分类测试通过")
        
        # 测试资源处理器
        metadata_extractor = MetadataExtractor()
        metadata = metadata_extractor.extract_from_url("https://example.com/test.pdf")
        assert metadata["file_type"] == "pdf"
        print("✅ 元数据提取测试通过")
        
        # 测试数据导出器
        exporter = DataExporter()
        print("✅ 数据导出器创建成功")
        
        print("🎉 所有集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def run_unit_tests():
    """运行单元测试"""
    print("🧪 开始单元测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestUniversalScraper))
    test_suite.addTest(unittest.makeSuite(TestResourceProcessors))
    test_suite.addTest(unittest.makeSuite(TestDataExporter))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 返回测试结果
    return result.wasSuccessful()

async def main():
    """主测试函数"""
    print("=" * 60)
    print("全站数据抓取工具 - 测试套件")
    print("=" * 60)
    
    # 运行单元测试
    unit_test_success = run_unit_tests()
    
    print("\n" + "=" * 60)
    
    # 运行集成测试
    integration_test_success = await run_integration_test()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"单元测试: {'✅ 通过' if unit_test_success else '❌ 失败'}")
    print(f"集成测试: {'✅ 通过' if integration_test_success else '❌ 失败'}")
    
    if unit_test_success and integration_test_success:
        print("🎉 所有测试通过，系统可以正常使用！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    asyncio.run(main())
