#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license         http://www.178188.xyz
@lastmodify      2025年8月1日
模块说明: 并发爬虫快速测试脚本
"""

import asyncio
import logging
import time

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

async def test_concurrent_import():
    """测试并发模块导入"""
    try:
        from concurrent_scraper import ConcurrentUniversalScraper, BrowserPool, TaskQueue, PerformanceMonitor
        print("✅ 并发爬虫模块导入成功")
        
        from config import CONCURRENT_CONFIG
        print("✅ 并发配置导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

async def test_concurrent_initialization():
    """测试并发爬虫初始化"""
    try:
        from concurrent_scraper import ConcurrentUniversalScraper
        
        # 测试不同模式的初始化
        modes = ["safe", "normal", "fast", "turbo"]
        
        for mode in modes:
            scraper = ConcurrentUniversalScraper(mode)
            print(f"✅ {mode} 模式初始化成功 - 浏览器数: {scraper.max_browsers}, 延迟: {scraper.base_delay}s")
        
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

async def test_browser_pool():
    """测试浏览器池"""
    try:
        from concurrent_scraper import BrowserPool
        
        print("🔄 测试浏览器池...")
        
        # 创建小型浏览器池用于测试
        pool = BrowserPool(max_browsers=2, pages_per_browser=1)
        
        # 初始化
        await pool.initialize()
        print(f"✅ 浏览器池初始化成功: {len(pool.browsers)} 个浏览器")
        
        # 测试页面获取和归还
        page_info = await pool.get_page()
        if page_info:
            print("✅ 页面获取成功")
            await pool.return_page(page_info)
            print("✅ 页面归还成功")
        
        # 清理
        await pool.close_all()
        print("✅ 浏览器池清理完成")
        
        return True
    except Exception as e:
        print(f"❌ 浏览器池测试失败: {e}")
        return False

async def test_task_queue():
    """测试任务队列"""
    try:
        from concurrent_scraper import TaskQueue
        
        print("🔄 测试任务队列...")
        
        queue = TaskQueue()
        
        # 添加测试任务
        test_tasks = [
            {"type": "test", "url": "http://example.com/1"},
            {"type": "test", "url": "http://example.com/2"},
            {"type": "test", "url": "http://example.com/3"}
        ]
        
        await queue.add_tasks(test_tasks)
        print(f"✅ 任务添加成功: {len(test_tasks)} 个任务")
        
        # 测试任务获取
        task = await queue.get_next_task()
        if task:
            print("✅ 任务获取成功")
            
            # 测试任务完成
            await queue.complete_task(task, "test_result")
            print("✅ 任务完成标记成功")
        
        # 获取统计
        stats = queue.get_stats()
        print(f"✅ 队列统计: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ 任务队列测试失败: {e}")
        return False

async def test_performance_monitor():
    """测试性能监控"""
    try:
        from concurrent_scraper import PerformanceMonitor
        
        print("🔄 测试性能监控...")
        
        monitor = PerformanceMonitor()
        
        # 模拟进度更新
        monitor.update_progress(5, 1, 10)
        
        # 获取统计
        stats = monitor.get_current_stats()
        print(f"✅ 性能统计获取成功: 进度 {stats['progress_percent']:.1f}%")
        
        # 测试进度打印
        monitor.print_progress()
        print()  # 换行
        
        return True
    except Exception as e:
        print(f"❌ 性能监控测试失败: {e}")
        return False

async def test_concurrent_basic_function():
    """测试并发爬虫基础功能"""
    try:
        from concurrent_scraper import ConcurrentUniversalScraper
        
        print("🔄 测试并发爬虫基础功能...")
        
        # 创建爬虫实例
        scraper = ConcurrentUniversalScraper("safe")  # 使用安全模式测试
        
        # 测试链接过滤
        test_links = {
            'https://www.zhaozhongai.com/product/123.html',
            'https://www.zhaozhongai.com/contact/',
            'https://external.com/test.html'
        }
        filtered = scraper._filter_links(test_links, 'https://www.zhaozhongai.com')
        print(f"✅ 链接过滤测试: {len(filtered)}/{len(test_links)} 个链接通过")
        
        # 测试页面分类
        page_type = scraper.classify_page_type('https://www.zhaozhongai.com/product/123.html')
        print(f"✅ 页面分类测试: product/123.html -> {page_type}")
        
        # 测试任务创建
        categorized_links = {
            "product_category": ["https://www.zhaozhongai.com/category1/"],
            "product_detail": ["https://www.zhaozhongai.com/product/1.html"],
            "download": ["https://www.zhaozhongai.com/download/"]
        }
        
        tasks = scraper._create_tasks_from_links(categorized_links, 10)
        print(f"✅ 任务创建测试: 创建 {len(tasks)} 个任务")
        
        return True
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    setup_test_logging()
    
    print("🚀 并发爬虫快速测试套件")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_concurrent_import),
        ("初始化测试", test_concurrent_initialization),
        ("浏览器池测试", test_browser_pool),
        ("任务队列测试", test_task_queue),
        ("性能监控测试", test_performance_monitor),
        ("基础功能测试", test_concurrent_basic_function)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！并发爬虫可以正常使用。")
        print("\n💡 使用建议:")
        print("• 新用户建议使用 'safe' 或 'normal' 模式")
        print("• 网络良好时可使用 'fast' 或 'turbo' 模式")
        print("• 运行 python turbo_scraper.py 开始使用")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
