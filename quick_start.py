#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license         http://www.178188.xyz
@lastmodify      2025年7月31日
模块说明: 兆众智能传感器爬虫快速启动脚本
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                兆众智能传感器数据爬取工具 v2.0                ║
    ║                                                              ║
    ║  功能特性:                                                   ║
    ║  • 支持9大传感器产品类别                                     ║
    ║  • 智能数据提取和清洗                                       ║
    ║  • 多格式输出 (JSON/CSV/Excel)                              ║
    ║  • 完整的错误处理和重试机制                                  ║
    ║                                                              ║
    ║  开发者: 苏昱                                                ║
    ║  更新时间: 2025年7月31日                                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['playwright', 'pandas', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        print("playwright install chromium")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def show_menu():
    """显示主菜单"""
    menu = """
    请选择操作:
    
    1. 🕷️  运行完整爬取 (推荐)
    2. 🧪 运行功能测试
    3. 📊 处理现有数据
    4. 🔍 快速测试单个类别
    5. 📖 查看使用指南
    6. 🚪 退出程序
    
    请输入选项编号 (1-6): """
    
    return input(menu).strip()

async def run_full_scraping():
    """运行完整爬取"""
    print("\n🚀 开始完整爬取...")
    print("预计耗时: 10-20分钟")
    print("爬取范围: 9大传感器类别")
    
    # 询问详情爬取数量
    while True:
        try:
            max_products = input("\n每个类别爬取多少个产品的详细信息? (建议5-15, 默认10): ").strip()
            if not max_products:
                max_products = 10
            else:
                max_products = int(max_products)
            
            if 1 <= max_products <= 50:
                break
            else:
                print("请输入1-50之间的数字")
        except ValueError:
            print("请输入有效数字")
    
    try:
        from sensor_scraper import EnhancedSensorScraper
        
        scraper = EnhancedSensorScraper()
        products = await scraper.scrape_all_products(max_products_per_category=max_products)
        
        if products:
            print(f"\n✅ 爬取成功! 共获取 {len(products)} 个产品")
            
            # 保存数据
            scraper.save_data("sensor_products_enhanced.json")
            scraper.export_to_csv("sensor_products_enhanced.csv")
            
            # 生成摘要
            summary = scraper.generate_summary()
            with open("scraping_report.json", 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            print("\n📁 生成的文件:")
            print("  • sensor_products_enhanced.json - 完整数据")
            print("  • sensor_products_enhanced.csv - CSV格式")
            print("  • scraping_report.json - 摘要报告")
            print("  • scraper.log - 详细日志")
            
            return True
        else:
            print("❌ 爬取失败，请查看日志文件")
            return False
            
    except Exception as e:
        print(f"❌ 爬取过程中出错: {e}")
        return False

async def run_tests():
    """运行功能测试"""
    print("\n🧪 开始功能测试...")
    
    try:
        from test_scraper import run_all_tests
        await run_all_tests()
        print("\n✅ 测试完成! 请查看 test_report.json")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def process_existing_data():
    """处理现有数据"""
    print("\n📊 处理现有数据...")
    
    # 查找可用的数据文件
    data_files = []
    for filename in ['sensor_products_enhanced.json', 'sensor_products.json']:
        if os.path.exists(filename):
            data_files.append(filename)
    
    if not data_files:
        print("❌ 未找到数据文件，请先运行爬取")
        return False
    
    print(f"找到数据文件: {', '.join(data_files)}")
    
    try:
        from data_processor import EnhancedSensorDataProcessor
        
        # 使用最新的数据文件
        processor = EnhancedSensorDataProcessor(data_files[0])
        
        if not processor.products_data:
            print("❌ 数据文件为空或格式错误")
            return False
        
        print(f"✅ 成功加载 {len(processor.products_data)} 个产品")
        
        # 生成各种表格
        processor.save_all_tables()
        
        print("\n📁 生成的表格文件:")
        print("  • 传感器产品分析表.xlsx")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理失败: {e}")
        return False

async def quick_test_category():
    """快速测试单个类别"""
    print("\n🔍 快速测试单个类别...")
    
    categories = {
        "1": ("接近传感器", "/jiejinchuanganqi/"),
        "2": ("光电传感器", "/guangdianchuanganqi/"),
        "3": ("激光光电传感器", "/jiguangguangdianchuanganqi/"),
        "4": ("色标传感器", "/sebiaochuanganqi/"),
        "5": ("光纤传感器", "/guangxianchuanganqi/")
    }
    
    print("可选类别:")
    for key, (name, _) in categories.items():
        print(f"  {key}. {name}")
    
    choice = input("\n请选择类别编号 (1-5): ").strip()
    
    if choice not in categories:
        print("❌ 无效选择")
        return False
    
    category_name, category_url = categories[choice]
    
    try:
        from sensor_scraper import EnhancedSensorScraper
        
        scraper = EnhancedSensorScraper()
        scraper.sensor_categories = {category_name: category_url}
        
        products = await scraper.scrape_all_products(max_products_per_category=3)
        
        if products:
            print(f"\n✅ 测试成功! {category_name} 获取到 {len(products)} 个产品")
            
            # 显示第一个产品信息
            if products:
                first = products[0]
                print(f"\n示例产品: {first.get('name')}")
                print(f"规格数量: {len(first.get('specifications', {}))}")
                print(f"描述数量: {len(first.get('descriptions', []))}")
            
            # 保存测试数据
            scraper.save_data(f"test_{category_name}.json")
            return True
        else:
            print("❌ 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def show_guide():
    """显示使用指南"""
    guide_file = "爬虫使用指南.md"
    
    if os.path.exists(guide_file):
        print(f"\n📖 使用指南位于: {guide_file}")
        print("请使用文本编辑器或Markdown阅读器打开查看")
    else:
        print("\n📖 使用指南:")
        print("""
        基本使用流程:
        1. 首次使用建议先运行功能测试
        2. 确认功能正常后运行完整爬取
        3. 使用数据处理功能生成表格
        4. 查看生成的文件和报告
        
        注意事项:
        • 确保网络连接稳定
        • 避免过于频繁的爬取
        • 重要数据请及时备份
        """)

async def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    while True:
        choice = show_menu()
        
        if choice == '1':
            await run_full_scraping()
        elif choice == '2':
            await run_tests()
        elif choice == '3':
            process_existing_data()
        elif choice == '4':
            await quick_test_category()
        elif choice == '5':
            show_guide()
        elif choice == '6':
            print("\n👋 感谢使用，再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        input("按回车键退出...")
